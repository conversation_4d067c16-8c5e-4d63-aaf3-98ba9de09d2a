"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _shared = __webpack_require__(/*! ./react-client-callbacks/shared */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/shared.js\");\nconst _approuter = __webpack_require__(/*! ./react-client-callbacks/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/app-router.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _actionqueue = __webpack_require__(/*! ../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter1 = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw new Error('Unexpected server data: missing bootstrap script.');\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw new Error('Unexpected server data: missing bootstrap script.');\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\n// React overrides `.then` and doesn't return a new promise chain,\n// so we wrap the action queue in a promise to ensure that its value\n// is defined when the promise resolves.\n// https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\nconst pendingActionQueue = new Promise((resolve, reject)=>{\n    initialServerResponse.then((initialRSCPayload)=>{\n        // setAppBuildId should be called only once, during JS initialization\n        // and before any components have hydrated.\n        (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n        resolve((0, _actionqueue.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n            initialFlightData: initialRSCPayload.f,\n            initialCanonicalUrlParts: initialRSCPayload.c,\n            initialParallelRoutes: new Map(),\n            location: window.location,\n            couldBeIntercepted: initialRSCPayload.i,\n            postponed: initialRSCPayload.s,\n            prerendered: initialRSCPayload.S\n        })));\n    }, (err)=>reject(err));\n});\nfunction ServerRoot() {\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter1.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _shared.onRecoverableError,\n    onCaughtError: _approuter.onCaughtError,\n    onUncaughtError: _approuter.onUncaughtError\n};\nfunction hydrate() {\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {})\n            })\n        })\n    });\n    const rootLayoutMissingTags = window.__next_root_layout_missing_tags;\n    const hasMissingTags = !!(rootLayoutMissingTags == null ? void 0 : rootLayoutMissingTags.length);\n    const isError = document.documentElement.id === '__next_error__' || hasMissingTags;\n    if (isError) {\n        if (true) {\n            const createDevOverlayElement = (__webpack_require__(/*! ./components/react-dev-overlay/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/client-entry.js\").createDevOverlayElement);\n            const errorTree = createDevOverlayElement(reactEl);\n            _client.default.createRoot(appElement, reactRootOptions).render(errorTree);\n        } else {}\n    } else {\n        _react.default.startTransition(()=>_client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            }));\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducer = __webpack_require__(/*! ./use-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _appcallserver = __webpack_require__(/*! ../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _prefetch = __webpack_require__(/*! ../components/segment-cache/prefetch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache/prefetch.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null\n    };\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        if (false) {}\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType,\n            allowAliasing: true\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    //\n    // @ts-expect-error The second argument to `useDeferredValue` is only\n    // available in the experimental builds. When its disabled, it will always\n    // return `head`.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { actionQueue, assetPrefix } = param;\n    const [state, dispatch] = (0, _usereducer.useReducer)(actionQueue);\n    const { canonicalUrl } = (0, _usereducer.useUnwrapState)(state);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl, typeof window === 'undefined' ? 'http://n' : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    (0, _appcallserver.useServerActionDispatcher)(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch:  false ? // cache. So we don't need to dispatch an action.\n            0 : (href, options)=>{\n                // Use the old prefetch implementation.\n                const url = createPrefetchURL(href);\n                if (url !== null) {\n                    (0, _react.startTransition)(()=>{\n                        var _options_kind;\n                        dispatch({\n                            type: _routerreducertypes.ACTION_PREFETCH,\n                            url,\n                            kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                        });\n                    });\n                }\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'replace', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'push', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            hmrRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_HMR_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        actionQueue,\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducer.useUnwrapState)(state);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    appRouter.push(url, {});\n                } else {\n                    appRouter.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, [\n        appRouter\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducer.useUnwrapState)(state);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducer.useUnwrapState)(state);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            childNodes: cache.parallelRoutes,\n            tree,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl,\n            loading: cache.loading\n        };\n    }, [\n        cache.parallelRoutes,\n        tree,\n        canonicalUrl,\n        cache.loading\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            changeByServerResponse,\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        changeByServerResponse,\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        if (typeof window !== 'undefined') {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            children: content\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducer.useUnwrapState)(state)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"bU8t8nCPb2ycaFr1siwKA2Gych0=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate\n    ];\n});\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        errorComponent: globalErrorComponent,\n        errorStyles: globalErrorStyles,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s1();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s1(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../react-dev-overlay/internal/helpers/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js\");\nconst originConsoleError = window.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (typeof window === 'undefined') {\n        return;\n    }\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const replayedError = matchReplayedError(...args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleClientError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args, true);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nfunction matchReplayedError() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    // See\n    // https://github.com/facebook/react/blob/65a56d0e99261481c721334a3ec4561d173594cd/packages/react-devtools-shared/src/backend/flight/renderer.js#L88-L93\n    //\n    // Logs replayed from the server look like this:\n    // [\n    //   \"%c%s%c %o\\n\\n%s\\n\\n%s\\n\",\n    //   \"background: #e6e6e6; ...\",\n    //   \" Server \", // can also be e.g. \" Prerender \"\n    //   \"\",\n    //   Error\n    //   \"The above error occurred in the <Page> component.\"\n    //   ...\n    // ]\n    if (args.length > 3 && typeof args[0] === 'string' && args[0].startsWith('%c%s%c ') && typeof args[1] === 'string' && typeof args[2] === 'string' && typeof args[3] === 'string') {\n        const maybeError = args[4];\n        if ((0, _iserror.default)(maybeError)) {\n            return maybeError;\n        }\n    }\n    return null;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _hash = __webpack_require__(/*! ../../../shared/lib/hash */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hash.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        const res = await createFetch(url, headers, fetchPriority);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeader = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeader !== null ? parseInt(staleTimeHeader, 10) : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority) {\n    const fetchUrl = new URL(url);\n    if (false) {}\n    // This is used to cache bust CDNs that don't support custom headers. The\n    // result is stored in a search param.\n    // TODO: Given that we have to use a search param anyway, we might as well\n    // _only_ use a search param and not bother with the custom headers.\n    // Add unique cache query to avoid caching conflicts on CDN which don't respect the Vary header\n    const uniqueCacheQuery = (0, _hash.hexHash)([\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] || '0',\n        headers[_approuterheaders.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n        headers[_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER],\n        headers[_approuterheaders.NEXT_URL]\n    ].join(','));\n    fetchUrl.searchParams.set(_approuterheaders.NEXT_RSC_UNION_QUERY, uniqueCacheQuery);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSegmentMismatch\", ({\n    enumerable: true,\n    get: function() {\n        return handleSegmentMismatch;\n    }\n}));\nconst _navigatereducer = __webpack_require__(/*! ./reducers/navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nfunction handleSegmentMismatch(state, action, treePatch) {\n    if (true) {\n        console.warn('Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' + 'Reason: Segment mismatch\\n' + (\"Last Action: \" + action.type + \"\\n\\n\") + (\"Current Tree: \" + JSON.stringify(state.tree) + \"\\n\\n\") + (\"Tree Patch Payload: \" + JSON.stringify(treePatch)));\n    }\n    return (0, _navigatereducer.handleExternalUrl)(state, {}, state.canonicalUrl, true);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-segment-mismatch.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/app-router.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/app-router.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/react-dev-overlay/internal/helpers/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/react-dev-overlay/internal/helpers/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst onCaughtError = (err, errorInfo)=>{\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_errorBoundary, _errorInfo_componentStack;\n        const errorBoundaryComponent = errorInfo == null ? void 0 : (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError, []);\n    } else {}\n};\nconst onUncaughtError = (err, errorInfo)=>{\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorLocation = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRouterContext: function() {\n        return AppRouterContext;\n    },\n    GlobalLayoutRouterContext: function() {\n        return GlobalLayoutRouterContext;\n    },\n    LayoutRouterContext: function() {\n        return LayoutRouterContext;\n    },\n    MissingSlotContext: function() {\n        return MissingSlotContext;\n    },\n    TemplateContext: function() {\n        return TemplateContext;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AppRouterContext = _react.default.createContext(null);\nconst LayoutRouterContext = _react.default.createContext(null);\nconst GlobalLayoutRouterContext = _react.default.createContext(null);\nconst TemplateContext = _react.default.createContext(null);\nif (true) {\n    AppRouterContext.displayName = 'AppRouterContext';\n    LayoutRouterContext.displayName = 'LayoutRouterContext';\n    GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext';\n    TemplateContext.displayName = 'TemplateContext';\n}\nconst MissingSlotContext = _react.default.createContext(new Set()); //# sourceMappingURL=app-router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HeadManagerContext\", ({\n    enumerable: true,\n    get: function() {\n        return HeadManagerContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst HeadManagerContext = _react.default.createContext({});\nif (true) {\n    HeadManagerContext.displayName = 'HeadManagerContext';\n} //# sourceMappingURL=head-manager-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9oZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEscUJBVVJDLE9BQUFBLE9BQUssQ0FBQ0MsYUFBYSxDQUFDLENBQUM7QUFFMUIsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekNILG1CQUFtQk0sV0FBVyxHQUFHO0FBQ25DIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxzcmNcXHNoYXJlZFxcbGliXFxoZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBjb25zdCBIZWFkTWFuYWdlckNvbnRleHQ6IFJlYWN0LkNvbnRleHQ8e1xuICB1cGRhdGVIZWFkPzogKHN0YXRlOiBhbnkpID0+IHZvaWRcbiAgbW91bnRlZEluc3RhbmNlcz86IGFueVxuICB1cGRhdGVTY3JpcHRzPzogKHN0YXRlOiBhbnkpID0+IHZvaWRcbiAgc2NyaXB0cz86IGFueVxuICBnZXRJc1Nzcj86ICgpID0+IGJvb2xlYW5cblxuICAvLyBVc2VkIGluIGFwcCBkaXJlY3RvcnksIHRvIHJlbmRlciBzY3JpcHQgdGFncyBhcyBzZXJ2ZXIgY29tcG9uZW50cy5cbiAgYXBwRGlyPzogYm9vbGVhblxuICBub25jZT86IHN0cmluZ1xufT4gPSBSZWFjdC5jcmVhdGVDb250ZXh0KHt9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBIZWFkTWFuYWdlckNvbnRleHQuZGlzcGxheU5hbWUgPSAnSGVhZE1hbmFnZXJDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIkhlYWRNYW5hZ2VyQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcc3JjXFxzaGFyZWRcXGxpYlxcaG9va3MtY2xpZW50LWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgUGFyYW1zIH0gZnJvbSAnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJ1xuXG5leHBvcnQgY29uc3QgU2VhcmNoUGFyYW1zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VVJMU2VhcmNoUGFyYW1zIHwgbnVsbD4obnVsbClcbmV4cG9ydCBjb25zdCBQYXRobmFtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PHN0cmluZyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFBhcmFtcyB8IG51bGw+KG51bGwpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFNlYXJjaFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnU2VhcmNoUGFyYW1zQ29udGV4dCdcbiAgUGF0aG5hbWVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhuYW1lQ29udGV4dCdcbiAgUGF0aFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnUGF0aFBhcmFtc0NvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiUGF0aFBhcmFtc0NvbnRleHQiLCJQYXRobmFtZUNvbnRleHQiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});