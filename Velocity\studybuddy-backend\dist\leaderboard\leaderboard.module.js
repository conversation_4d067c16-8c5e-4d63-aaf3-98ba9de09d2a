"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const leaderboard_controller_1 = require("./leaderboard.controller");
const leaderboard_service_1 = require("./leaderboard.service");
const user_schema_1 = require("../schemas/user.schema");
const userDetails_schema_1 = require("../schemas/userDetails.schema");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
const jwtConfig_1 = require("../config/jwtConfig");
let LeaderboardModule = class LeaderboardModule {
};
exports.LeaderboardModule = LeaderboardModule;
exports.LeaderboardModule = LeaderboardModule = __decorate([
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.registerAsync(jwtConfig_1.default.asProvider()),
            mongoose_1.MongooseModule.forFeature([
                { name: user_schema_1.User.name, schema: user_schema_1.UserSchema },
                { name: userDetails_schema_1.UserDetails.name, schema: userDetails_schema_1.UserDetailsSchema },
                { name: chatHistory_schema_1.ChatHistory.name, schema: chatHistory_schema_1.ChatHistorySchema }
            ])
        ],
        providers: [leaderboard_service_1.LeaderboardService],
        controllers: [leaderboard_controller_1.LeaderboardController],
        exports: [leaderboard_service_1.LeaderboardService]
    })
], LeaderboardModule);
//# sourceMappingURL=leaderboard.module.js.map