"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingUsers, setIsLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [decryptionCache, setDecryptionCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3001/api\", \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to delete user:', err);\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const refreshUsers = ()=>{\n        // Force a page reload to refresh data\n        window.location.reload();\n    };\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3001/api\", \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to register user:', err);\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval and user fetching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            const loadData = {\n                \"AdminDashboard.useEffect.loadData\": async ()=>{\n                    if (true) {\n                        const stored = localStorage.getItem('accessToken');\n                        if (!stored) {\n                            router.push('/admin-login');\n                            return;\n                        }\n                        setToken(stored);\n                        setIsLoadingUsers(true);\n                        try {\n                            const headers = {\n                                Authorization: \"Bearer \".concat(stored),\n                                'Content-Type': 'application/json'\n                            };\n                            const res = await fetch(\"\".concat(\"http://localhost:3001/api\", \"/users\"), {\n                                headers,\n                                cache: 'no-cache'\n                            });\n                            if (!res.ok) {\n                                throw new Error(\"fetch failed: \".concat(res.status));\n                            }\n                            const data = await res.json();\n                            console.log(\"Fetched users data:\", data);\n                            const list = await Promise.all(data.map({\n                                \"AdminDashboard.useEffect.loadData\": async (u)=>({\n                                        ...u,\n                                        decryptedPassword: await decryptPassword(u.password)\n                                    })\n                            }[\"AdminDashboard.useEffect.loadData\"]));\n                            setUsers(list);\n                            console.log(\"Users state set to:\", list.length, \"users\");\n                        } catch (error) {\n                            console.error(\"Error loading users:\", error);\n                            toast({\n                                title: 'Error',\n                                description: 'Failed to load users'\n                            });\n                        } finally{\n                            setIsLoadingUsers(false);\n                        }\n                    }\n                }\n            }[\"AdminDashboard.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[getAuthHeaders]\": ()=>({\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            })\n    }[\"AdminDashboard.useCallback[getAuthHeaders]\"], [\n        token\n    ]);\n    const decryptPassword = async (encrypted)=>{\n        // Return early if no encrypted password or empty string\n        if (!encrypted || encrypted.trim() === '') {\n            return encrypted;\n        }\n        // Check cache first\n        if (decryptionCache.has(encrypted)) {\n            return decryptionCache.get(encrypted);\n        }\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            // Cache the result\n            setDecryptionCache((prev)=>new Map(prev).set(encrypted, data.decryptedPassword));\n            return data.decryptedPassword;\n        } catch (error) {\n            console.error('Decryption failed for:', encrypted, error);\n            return encrypted;\n        }\n    };\n    // Debug log for users state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            console.log(\"Users state changed:\", users.length, \"users\") // Debug log\n            ;\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        users\n    ]);\n    // Debug log for loading state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            console.log(\"Loading state changed:\", isLoadingUsers) // Debug log\n            ;\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        isLoadingUsers\n    ]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-2 bg-yellow-100 text-xs\",\n                                children: [\n                                    \"Debug: users.length=\",\n                                    users.length,\n                                    \", isLoading=\",\n                                    isLoadingUsers,\n                                    \", token=\",\n                                    token ? 'present' : 'missing',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"API URL: \",\n                                    \"http://localhost:3001/api\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                title: \"Students\",\n                                count: users.length,\n                                columns: columns,\n                                data: users,\n                                actions: actions,\n                                onAddNew: ()=>setIsModalOpen(true),\n                                addButtonLabel: \"Add User\",\n                                page: page,\n                                pageSize: pageSize,\n                                onPageChange: setPage,\n                                isLoading: isLoadingUsers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"PAnj+IJckq76NaqXfAAFp7o19AQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/student-details-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/student-details-modal.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction StudentDetailsModal(param) {\n    let { isOpen, onClose, student } = param;\n    var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_chatStats5, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_quizStats5, _analytics_quizStats6, _analytics_quizStats7, _analytics_quizStats8, _analytics_activityPattern;\n    _s();\n    console.log('StudentDetailsModal rendered with:', {\n        isOpen,\n        student: student === null || student === void 0 ? void 0 : student._id\n    });\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [studentInfo, setStudentInfo] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [subjectMap, setSubjectMap] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [subjectsLoading, setSubjectsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const modalContentRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Fetch subjects for mapping IDs to names\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"StudentDetailsModal.useEffect\": ()=>{\n            const fetchSubjects = {\n                \"StudentDetailsModal.useEffect.fetchSubjects\": async ()=>{\n                    try {\n                        setSubjectsLoading(true);\n                        const response = await fetch(\"\".concat(\"http://localhost:3001/api\" || 0, \"/admin/subjects\"), {\n                            headers: getAuthHeaders()\n                        });\n                        if (response.ok) {\n                            const subjects = await response.json();\n                            const mapping = {};\n                            subjects.forEach({\n                                \"StudentDetailsModal.useEffect.fetchSubjects\": (subject)=>{\n                                    mapping[subject._id] = subject;\n                                }\n                            }[\"StudentDetailsModal.useEffect.fetchSubjects\"]);\n                            setSubjectMap(mapping);\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch subjects:', error);\n                    } finally{\n                        setSubjectsLoading(false);\n                    }\n                }\n            }[\"StudentDetailsModal.useEffect.fetchSubjects\"];\n            if (isOpen) {\n                fetchSubjects();\n            }\n        }\n    }[\"StudentDetailsModal.useEffect\"], [\n        isOpen\n    ]);\n    // Fetch student analytics when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"StudentDetailsModal.useEffect\": ()=>{\n            if (isOpen && (student === null || student === void 0 ? void 0 : student._id)) {\n                console.log('Modal opened for student:', student._id);\n                fetchStudentAnalytics();\n            }\n        }\n    }[\"StudentDetailsModal.useEffect\"], [\n        isOpen,\n        student === null || student === void 0 ? void 0 : student._id\n    ]) // fetchStudentAnalytics is stable, no need to add as dependency\n    ;\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Authorization': \"Bearer \".concat(token),\n            'Content-Type': 'application/json'\n        };\n    };\n    // Helper function to convert subject ID to name\n    const getSubjectDisplayName = (subjectId)=>{\n        const subject = subjectMap[subjectId];\n        return subject ? subject.name : subjectId // Fallback to ID if not found\n        ;\n    };\n    // Simple pie chart component for quiz distribution\n    const QuizDistributionChart = (param)=>{\n        let { data } = param;\n        const total = Object.values(data).reduce((sum, val)=>sum + val, 0);\n        if (total === 0) return null;\n        const colors = [\n            '#3B82F6',\n            '#EF4444',\n            '#10B981',\n            '#F59E0B',\n            '#8B5CF6'\n        ];\n        let currentAngle = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            \"data-chart\": \"pie\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-3\",\n                    style: {\n                        width: '150px',\n                        height: '150px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"150\",\n                        height: \"150\",\n                        viewBox: \"0 0 150 150\",\n                        className: \"transform -rotate-90\",\n                        style: {\n                            display: 'block'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"75\",\n                                cy: \"75\",\n                                r: \"60\",\n                                fill: \"none\",\n                                stroke: \"#e5e7eb\",\n                                strokeWidth: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            Object.entries(data).map((param, index)=>{\n                                let [subject, count] = param;\n                                const percentage = count / total * 100;\n                                const angle = percentage / 100 * 360;\n                                const startAngle = currentAngle;\n                                currentAngle += angle;\n                                const x1 = 75 + 60 * Math.cos(startAngle * Math.PI / 180);\n                                const y1 = 75 + 60 * Math.sin(startAngle * Math.PI / 180);\n                                const x2 = 75 + 60 * Math.cos((startAngle + angle) * Math.PI / 180);\n                                const y2 = 75 + 60 * Math.sin((startAngle + angle) * Math.PI / 180);\n                                const largeArcFlag = angle > 180 ? 1 : 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 75 75 L \".concat(x1, \" \").concat(y1, \" A 60 60 0 \").concat(largeArcFlag, \" 1 \").concat(x2, \" \").concat(y2, \" Z\"),\n                                    fill: colors[index % colors.length],\n                                    stroke: \"#ffffff\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:opacity-80 transition-opacity\"\n                                }, subject, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-2 text-xs max-w-sm\",\n                    children: Object.entries(data).map((param, index)=>{\n                        let [subject, count] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-sm\",\n                                    style: {\n                                        backgroundColor: colors[index % colors.length]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        getSubjectDisplayName(subject),\n                                        \": \",\n                                        count\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, subject, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    };\n    // Simple bar chart for quiz accuracy\n    const AccuracyBarChart = (param)=>{\n        let { data } = param;\n        if (!data || Object.keys(data).length === 0) return null;\n        const maxScore = Math.max(...Object.values(data));\n        const colors = [\n            '#3B82F6',\n            '#EF4444',\n            '#10B981',\n            '#F59E0B',\n            '#8B5CF6'\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2 p-3 bg-white rounded-lg\",\n            \"data-chart\": \"bar\",\n            style: {\n                width: '200px',\n                minHeight: '120px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"text-center font-medium text-gray-800 mb-2 text-sm\",\n                    children: \"Subject-wise Accuracy\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                Object.entries(data).map((param, index)=>{\n                    let [subject, score] = param;\n                    const percentage = Math.round(score * 100);\n                    const barWidth = maxScore > 0 ? score / maxScore * 100 : 0;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-700\",\n                                        children: getSubjectDisplayName(subject)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-900\",\n                                        children: [\n                                            percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-4 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 rounded-full transition-all duration-300 flex items-center justify-end pr-2\",\n                                    style: {\n                                        width: \"\".concat(Math.max(barWidth, 10), \"%\"),\n                                        backgroundColor: colors[index % colors.length]\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: [\n                                            percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, subject, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this);\n                })\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    };\n    const fetchStudentAnalytics = async ()=>{\n        if (!(student === null || student === void 0 ? void 0 : student._id)) return;\n        console.log('Fetching analytics for student:', student._id);\n        console.log('API URL:', \"\".concat(\"http://localhost:3001/api\" || 0, \"/admin/analytics/student/\").concat(student._id));\n        setIsLoading(true);\n        try {\n            // First, try to get user details from the existing users API\n            const userResponse = await fetch(\"\".concat(\"http://localhost:3001/api\" || 0, \"/users\"), {\n                headers: getAuthHeaders()\n            });\n            let userDetails = null;\n            if (userResponse.ok) {\n                const users = await userResponse.json();\n                userDetails = users.find((u)=>u._id === student._id);\n                console.log('Found user details:', userDetails);\n            }\n            // Try to fetch analytics from the new endpoint\n            const response = await fetch(\"\".concat(\"http://localhost:3001/api\" || 0, \"/admin/analytics/student/\").concat(student._id), {\n                headers: getAuthHeaders()\n            });\n            console.log('Analytics API response status:', response.status);\n            if (response.ok) {\n                const data = await response.json();\n                console.log('Analytics data received:', data);\n                console.log('Analytics structure:', JSON.stringify(data.analytics, null, 2));\n                // Set student info from API response\n                if (data.studentInfo) {\n                    setStudentInfo(data.studentInfo);\n                }\n                // Set analytics data from API\n                if (data.analytics) {\n                    console.log('Setting analytics data:', data.analytics);\n                    setAnalytics(data.analytics);\n                } else {\n                    console.log('No analytics data received, showing empty state');\n                    setAnalytics(createFallbackAnalytics());\n                }\n            } else {\n                console.log('Analytics API not available, status:', response.status);\n                // Show empty state instead of dummy data\n                setAnalytics(createFallbackAnalytics());\n                toast({\n                    title: \"Info\",\n                    description: \"Analytics service is not available. Showing empty state.\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching student analytics:', error);\n            // Show empty state instead of dummy data\n            setAnalytics(createFallbackAnalytics());\n            toast({\n                title: \"Error\",\n                description: \"Failed to load analytics data. Please try refreshing.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createFallbackAnalytics = ()=>{\n        // Return empty data when no real analytics available\n        return {\n            quizStats: {\n                totalAttempted: 0,\n                accuracy: 0,\n                subjectWiseAttempts: {},\n                averageScores: {},\n                lastQuizDate: 'No quizzes taken yet',\n                topicsCompleted: 0\n            },\n            chatStats: {\n                totalMessages: 0,\n                totalDoubts: 0,\n                mostDiscussedSubject: 'No chat activity yet',\n                totalTimeSpent: '0hr 0min',\n                timeOfDayMostActive: 'No activity yet',\n                streak: 0\n            },\n            leaderboardStats: {\n                currentRank: 0,\n                sparkPoints: 0,\n                rankMovement: 'No rank yet',\n                motivationLevel: 'Getting started'\n            },\n            activityPattern: {\n                dailyActivity: [],\n                weeklyPattern: {},\n                monthlyTrend: []\n            }\n        };\n    };\n    const generatePDFReport = async ()=>{\n        if (!modalContentRef.current || !displayStudentInfo) return;\n        try {\n            // Create a new PDF document\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_4__[\"default\"]('p', 'mm', 'a4');\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            let yPosition = 20;\n            // Add title\n            pdf.setFontSize(20);\n            pdf.setFont('helvetica', 'bold');\n            pdf.text('Student Analytics Report', pageWidth / 2, yPosition, {\n                align: 'center'\n            });\n            yPosition += 15;\n            // Add generation date\n            pdf.setFontSize(10);\n            pdf.setFont('helvetica', 'normal');\n            pdf.text(\"Generated on: \".concat(new Date().toLocaleDateString()), pageWidth / 2, yPosition, {\n                align: 'center'\n            });\n            yPosition += 20;\n            // Student Information Section\n            pdf.setFontSize(14);\n            pdf.setFont('helvetica', 'bold');\n            pdf.text('Student Information', 20, yPosition);\n            yPosition += 10;\n            pdf.setFontSize(10);\n            pdf.setFont('helvetica', 'normal');\n            const studentInfo = [\n                \"Name: \".concat(displayStudentInfo.name || 'Unknown Student'),\n                \"Email: \".concat(displayStudentInfo.email),\n                \"Phone: \".concat(displayStudentInfo.phone || 'Not provided'),\n                \"Class: \".concat(displayStudentInfo.class || 'Not specified'),\n                \"School: \".concat(displayStudentInfo.schoolName || 'Not specified'),\n                \"Registration Date: \".concat(new Date(displayStudentInfo.createdAt).toLocaleDateString()),\n                \"Subjects: \".concat(displayStudentInfo.subjects && displayStudentInfo.subjects.length > 0 ? displayStudentInfo.subjects.join(', ') : 'None specified')\n            ];\n            studentInfo.forEach((info)=>{\n                pdf.text(info, 20, yPosition);\n                yPosition += 6;\n            });\n            yPosition += 10;\n            // Analytics Section\n            if (analytics) {\n                var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_quizStats5;\n                pdf.setFontSize(14);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Performance Analytics', 20, yPosition);\n                yPosition += 10;\n                // Quiz Statistics\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Quiz Statistics', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const quizStats = [\n                    \"Quizzes Attempted: \".concat(((_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0),\n                    \"Quiz Accuracy: \".concat(((_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0, \"%\"),\n                    \"Topics Completed: \".concat(((_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0),\n                    \"Last Quiz Date: \".concat(((_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'No quizzes taken')\n                ];\n                quizStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                // Add subject-wise performance if available\n                if (((_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0) {\n                    yPosition += 5;\n                    pdf.setFontSize(11);\n                    pdf.setFont('helvetica', 'bold');\n                    pdf.text('Subject-wise Performance:', 25, yPosition);\n                    yPosition += 6;\n                    pdf.setFontSize(10);\n                    pdf.setFont('helvetica', 'normal');\n                    Object.entries(analytics.quizStats.averageScores).forEach((param)=>{\n                        let [subjectKey, score] = param;\n                        pdf.text(\"\".concat(getSubjectDisplayName(subjectKey), \": \").concat(Math.round(score * 100), \"%\"), 30, yPosition);\n                        yPosition += 5;\n                    });\n                }\n                yPosition += 10;\n                // Chat Statistics\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Chat Statistics', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const chatStats = [\n                    \"Total Messages: \".concat(((_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0),\n                    \"Total Doubts Asked: \".concat(((_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0),\n                    \"Most Discussed Subject: \".concat(((_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) ? getSubjectDisplayName(analytics.chatStats.mostDiscussedSubject) : 'No chat activity'),\n                    \"Total Time Spent: \".concat(((_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || '0hr 0min'),\n                    \"Learning Streak: \".concat(((_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.streak) || 0, \" days\")\n                ];\n                chatStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                yPosition += 10;\n                // Leaderboard Stats\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Leaderboard Position', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const leaderboardStats = [\n                    \"Current Rank: \".concat(((_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked'),\n                    \"Spark Points: \".concat(((_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0),\n                    \"Rank Movement: \".concat(((_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'No movement'),\n                    \"Motivation Level: \".concat(((_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'Getting started')\n                ];\n                leaderboardStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                yPosition += 15;\n                // Add charts on a new page if data exists\n                if (((_analytics_quizStats5 = analytics.quizStats) === null || _analytics_quizStats5 === void 0 ? void 0 : _analytics_quizStats5.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0) {\n                    try {\n                        // Add a new page for charts\n                        pdf.addPage();\n                        let chartYPosition = 20;\n                        // Add charts page title\n                        pdf.setFontSize(18);\n                        pdf.setFont('helvetica', 'bold');\n                        pdf.text('Performance Charts', pageWidth / 2, chartYPosition, {\n                            align: 'center'\n                        });\n                        chartYPosition += 30;\n                        // Wait a moment for charts to render properly\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        // Find chart elements\n                        const pieChartElement = document.querySelector('[data-chart=\"pie\"]');\n                        const barChartElement = document.querySelector('[data-chart=\"bar\"]');\n                        if (pieChartElement) {\n                            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(pieChartElement, {\n                                backgroundColor: '#ffffff',\n                                scale: 3,\n                                useCORS: true,\n                                allowTaint: true,\n                                width: pieChartElement.offsetWidth,\n                                height: pieChartElement.offsetHeight,\n                                logging: false\n                            });\n                            const imgData = canvas.toDataURL('image/png', 1.0);\n                            pdf.setFontSize(14);\n                            pdf.setFont('helvetica', 'bold');\n                            pdf.text('Quiz Attempts by Subject', pageWidth / 2, chartYPosition, {\n                                align: 'center'\n                            });\n                            chartYPosition += 10;\n                            // Add pie chart - smaller and properly centered\n                            const chartWidth = 60;\n                            const chartHeight = 50;\n                            const chartX = (pageWidth - chartWidth) / 2;\n                            pdf.addImage(imgData, 'PNG', chartX, chartYPosition, chartWidth, chartHeight);\n                            chartYPosition += chartHeight + 15;\n                        }\n                        if (barChartElement) {\n                            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(barChartElement, {\n                                backgroundColor: '#ffffff',\n                                scale: 3,\n                                useCORS: true,\n                                allowTaint: true,\n                                width: barChartElement.offsetWidth,\n                                height: barChartElement.offsetHeight,\n                                logging: false\n                            });\n                            const imgData = canvas.toDataURL('image/png', 1.0);\n                            pdf.setFontSize(14);\n                            pdf.setFont('helvetica', 'bold');\n                            pdf.text('Subject-wise Accuracy Performance', pageWidth / 2, chartYPosition, {\n                                align: 'center'\n                            });\n                            chartYPosition += 10;\n                            // Add bar chart - smaller and properly centered\n                            const chartWidth = 80;\n                            const chartHeight = 60;\n                            const chartX = (pageWidth - chartWidth) / 2;\n                            pdf.addImage(imgData, 'PNG', chartX, chartYPosition, chartWidth, chartHeight);\n                        }\n                    } catch (chartError) {\n                        console.error('Error adding charts to PDF:', chartError);\n                        // Add text note about charts\n                        pdf.setFontSize(12);\n                        pdf.text('Charts could not be generated. Please view them in the web interface.', 20, 50);\n                    }\n                }\n            }\n            // Save the PDF\n            const fileName = \"student-report-\".concat(displayStudentInfo.name || (student === null || student === void 0 ? void 0 : student.email.split('@')[0]) || 'unknown', \"-\").concat(new Date().toISOString().split('T')[0], \".pdf\");\n            pdf.save(fileName);\n            toast({\n                title: \"Download Complete\",\n                description: \"Student report has been downloaded as PDF with charts.\"\n            });\n        } catch (error) {\n            console.error('Error generating PDF:', error);\n            throw error;\n        }\n    };\n    const handleDownload = async ()=>{\n        if (!(student === null || student === void 0 ? void 0 : student._id)) return;\n        setIsDownloading(true);\n        try {\n            await generatePDFReport();\n        } catch (error) {\n            console.error('Error downloading report:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to generate PDF report. Please try again.\"\n            });\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Helper function to generate text report\n    const generateTextReport = (studentInfo, analytics)=>{\n        var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_chatStats5, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_activityPattern;\n        const reportDate = new Date().toLocaleDateString();\n        const studentName = studentInfo.name || 'Unknown Student';\n        return \"\\nSTUDYBUDDY STUDENT REPORT\\nGenerated on: \".concat(reportDate, \"\\n\\nSTUDENT INFORMATION\\n==================\\nName: \").concat(studentName, \"\\nEmail: \").concat(studentInfo.email, \"\\nPhone: \").concat(studentInfo.phone || 'Not provided', \"\\nClass: \").concat(studentInfo.class || 'Not specified', \"\\nSchool: \").concat(studentInfo.schoolName || 'Not specified', \"\\nRegistration Date: \").concat(new Date(studentInfo.createdAt).toLocaleDateString(), \"\\nSubjects: \").concat(studentInfo.subjects && studentInfo.subjects.length > 0 ? studentInfo.subjects.join(', ') : 'None specified', \"\\n\\nPERFORMANCE ANALYTICS\\n====================\\n\").concat(analytics ? \"\\nQUIZ STATISTICS\\n--------------\\nQuizzes Attempted: \".concat(((_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0, \"\\nQuiz Accuracy: \").concat(((_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0, \"%\\nTopics Completed: \").concat(((_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0, \"\\nLast Quiz Date: \").concat(((_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'No quizzes taken', \"\\n\\nSUBJECT SCORES\\n--------------\\n\").concat(((_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 ? Object.entries(analytics.quizStats.averageScores).map((param)=>{\n            let [subject, score] = param;\n            return \"\".concat(subject, \": \").concat(Math.round(score * 100), \"%\");\n        }).join('\\n') : 'No quiz scores available', \"\\n\\nCHAT STATISTICS\\n--------------\\nTotal Messages Sent: \").concat(((_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0, \"\\nTotal Doubts Asked: \").concat(((_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0, \"\\nMost Discussed Subject: \").concat(((_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) || 'No chat activity', \"\\nTotal Time Spent: \").concat(((_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || '0hr 0min', \"\\nMost Active Time: \").concat(((_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.timeOfDayMostActive) || 'No activity', \"\\nLearning Streak: \").concat(((_analytics_chatStats5 = analytics.chatStats) === null || _analytics_chatStats5 === void 0 ? void 0 : _analytics_chatStats5.streak) || 0, \" days\\n\\nLEADERBOARD STATS\\n----------------\\nCurrent Rank: \").concat(((_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked', \"\\nSpark Points: \").concat(((_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0, \"\\nRank Movement: \").concat(((_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'No movement', \"\\nMotivation Level: \").concat(((_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'Getting started', \"\\n\\nRECENT ACTIVITY\\n--------------\\n\").concat(((_analytics_activityPattern = analytics.activityPattern) === null || _analytics_activityPattern === void 0 ? void 0 : _analytics_activityPattern.dailyActivity) && analytics.activityPattern.dailyActivity.length > 0 ? analytics.activityPattern.dailyActivity.slice(-7).map((activity)=>\"\".concat(new Date(activity.date).toLocaleDateString(), \": \").concat(activity.queries, \" queries, \").concat(activity.timeSpent, \" min, Subjects: \").concat(activity.subjects.map((subjectId)=>getSubjectDisplayName(subjectId)).join(', ') || 'None')).join('\\n') : 'No recent activity', \"\\n\") : 'Analytics data not available', \"\\n\\nReport generated by StudyBuddy Admin Dashboard\\n    \").trim();\n    };\n    if (!isOpen || !student) return null;\n    // Enhanced student info with better fallback data\n    const displayStudentInfo = studentInfo || {\n        userId: student._id,\n        name: student.name || \"Student \".concat(student.email.split('@')[0]),\n        phone: student.phone || \"Not provided\",\n        class: student.class || \"Not specified\",\n        schoolName: student.schoolName || \"Not specified\",\n        email: student.email,\n        createdAt: student.createdAt || student.createdOn || new Date().toISOString(),\n        subjects: student.subjects || [],\n        profileImage: student.avatar || student.profileImage || undefined\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[95vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-6 h-6 text-primary-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Student Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    onClick: fetchStudentAnalytics,\n                                    disabled: isLoading,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isDownloading,\n                                    className: \"bg-primary-blue hover:bg-primary-blue/90 text-white px-4 py-2 text-sm flex items-center\",\n                                    children: [\n                                        isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        isDownloading ? 'Generating...' : 'Download Details'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 698,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: modalContentRef,\n                    className: \"p-6 space-y-8\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 animate-spin text-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Loading student analytics...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            analytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: studentInfo ? 'Live data from analytics service' : 'Sample data - Analytics service unavailable'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-dark rounded-lg p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-32 h-32 bg-primary-blue rounded-lg flex items-center justify-center overflow-hidden\",\n                                            children: displayStudentInfo.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: displayStudentInfo.profileImage,\n                                                alt: displayStudentInfo.name,\n                                                className: \"w-full h-full object-cover rounded-lg\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = 'none';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-16 h-16 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Class\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.class\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"School Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.schoolName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-sm\",\n                                                                            children: \"Created On\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: new Date(displayStudentInfo.createdAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-sm\",\n                                                                            children: \"Subjects\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: displayStudentInfo.subjects && displayStudentInfo.subjects.length > 0 ? displayStudentInfo.subjects.join(', ') : 'Not specified'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Student Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Quiz Performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Quizzes Attempted:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Quiz Accuracy:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Topics Completed:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 830,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Last Quiz Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 && analytics.quizStats.totalAttempted > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Subject-wise Performance:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-1\",\n                                                                children: Object.entries(analytics.quizStats.averageScores).map((param)=>{\n                                                                    let [subjectKey, score] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600 capitalize\",\n                                                                                children: [\n                                                                                    getSubjectDisplayName(subjectKey),\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    Math.round(score * 100),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, subjectKey, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Chat Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Messages:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Doubts Asked:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Most Discussed Subject:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) ? getSubjectDisplayName(analytics.chatStats.mostDiscussedSubject) : 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Time Spent:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Most Active Time:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.timeOfDayMostActive) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 879,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Learning Streak:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats5 = analytics.chatStats) === null || _analytics_chatStats5 === void 0 ? void 0 : _analytics_chatStats5.streak) || 0,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Leaderboard Position\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Current Rank:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Spark Points:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Rank Movement:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Motivation Level:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 11\n                            }, this),\n                            analytics && (((_analytics_quizStats5 = analytics.quizStats) === null || _analytics_quizStats5 === void 0 ? void 0 : _analytics_quizStats5.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0 || ((_analytics_quizStats6 = analytics.quizStats) === null || _analytics_quizStats6 === void 0 ? void 0 : _analytics_quizStats6.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Performance Charts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            ((_analytics_quizStats7 = analytics.quizStats) === null || _analytics_quizStats7 === void 0 ? void 0 : _analytics_quizStats7.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-4\",\n                                                        children: \"Quiz Attempts by Subject\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuizDistributionChart, {\n                                                        data: analytics.quizStats.subjectWiseAttempts\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, this),\n                                            ((_analytics_quizStats8 = analytics.quizStats) === null || _analytics_quizStats8 === void 0 ? void 0 : _analytics_quizStats8.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-4\",\n                                                        children: \"Subject-wise Accuracy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AccuracyBarChart, {\n                                                        data: analytics.quizStats.averageScores\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 13\n                            }, this),\n                            (analytics === null || analytics === void 0 ? void 0 : (_analytics_activityPattern = analytics.activityPattern) === null || _analytics_activityPattern === void 0 ? void 0 : _analytics_activityPattern.dailyActivity) && analytics.activityPattern.dailyActivity.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: analytics.activityPattern.dailyActivity.slice(-7).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                        children: new Date(activity.date).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-xs text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Queries: \",\n                                                                    activity.queries\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Time: \",\n                                                                    activity.timeSpent,\n                                                                    \" min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 953,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Subjects: \",\n                                                                    activity.subjects.map((subjectId)=>getSubjectDisplayName(subjectId)).join(', ') || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, this) : !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No Activity Data Available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Activity data will appear once the student starts using the platform.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            onClick: fetchStudentAnalytics,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex items-center mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh Data\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 733,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 696,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n        lineNumber: 695,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentDetailsModal, \"6Xod99eRaXsIVofbq5cA2TGRCRk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = StudentDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"StudentDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/student-details-modal.tsx\n"));

/***/ })

});