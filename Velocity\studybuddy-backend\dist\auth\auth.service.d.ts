import { Model } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import { RegisterDto } from 'src/dtos/register.dto';
import { LoginDto } from 'src/dtos/login.dto';
import { JwtService } from '@nestjs/jwt';
export declare class AuthService {
    private userModel;
    private jwtService;
    constructor(userModel: Model<User>, jwtService: JwtService);
    login(loginDto: LoginDto): Promise<{
        accessToken: string;
        isUserDetailsPresent: boolean;
    }>;
    register(registerDto: RegisterDto): Promise<{
        success: boolean;
    }>;
}
