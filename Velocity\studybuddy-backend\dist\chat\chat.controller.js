"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const chat_service_1 = require("./chat.service");
const chatQueryDto_1 = require("../dtos/chatQueryDto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const jwt_auth_guard_1 = require("../guard/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
let ChatController = class ChatController {
    constructor(chatService) {
        this.chatService = chatService;
    }
    async getEducationalChat(req, query) {
        const queryDto = (0, class_transformer_1.plainToInstance)(chatQueryDto_1.ChatQueryDto, query);
        const errors = await (0, class_validator_1.validate)(queryDto);
        if (errors.length > 0) {
            throw new common_1.BadRequestException('Invalid query parameters');
        }
        const { subject, query: userQuery, topic } = queryDto;
        const response = await this.chatService.getChatResponse(req["userID"], subject, userQuery, topic);
        return { response };
    }
    async getHistoryForDay(req, date) {
        if (date == null) {
            return await this.chatService.getHistoryofLastFive(req['userID']);
        }
        return await this.chatService.getHistoryForDay(req['userID'], date);
    }
    async getFilteredChatHistory(req, upperBound, lowerBound) {
        return await this.chatService.getFilteredChatHistory(req['userID'], lowerBound, upperBound);
    }
    async getRecentTopics(req) {
        return await this.chatService.getRecentTopics(req['userID']);
    }
    async getTopicChatHistory(req, topic) {
        return await this.chatService.getTopicChatHistory(req['userID'], topic);
    }
    async extractTopicsFromHistory(req) {
        return await this.chatService.extractTopicsFromHistory(req['userID']);
    }
    async debugTopics(req) {
        return await this.chatService.debugTopics(req['userID']);
    }
    async getChatStreak(req) {
        return await this.chatService.getChatStreak(req['userID']);
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)(),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get AI chat response',
        description: 'Send a query to the AI tutor and get an educational response'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'subject',
        description: 'The subject for the educational query',
        example: 'Mathematics'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'query',
        description: 'The user\'s question or query',
        example: 'Explain quadratic equations'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'topic',
        description: 'The topic for the educational query (optional)',
        required: false,
        example: 'Quadratic Equations'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chat response received',
        schema: {
            type: 'object',
            properties: {
                response: {
                    type: 'string',
                    description: 'AI-generated educational response'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid query parameters' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Authentication required' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chatQueryDto_1.ChatQueryDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getEducationalChat", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("chat-history"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getHistoryForDay", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("heat-map"),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('upperBound')),
    __param(2, (0, common_1.Query)('lowerBound')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getFilteredChatHistory", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("recent-topics"),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get recent topics for user',
        description: 'Retrieve all unique topics the user has discussed'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Recent topics retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Authentication required' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getRecentTopics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("topic-history"),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get chat history for a specific topic',
        description: 'Retrieve chat history filtered by a specific topic'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'topic',
        description: 'The topic to filter chat history by',
        example: 'Quadratic Equations'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Topic chat history retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Authentication required' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('topic')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getTopicChatHistory", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("extract-topics"),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Extract potential topics from existing chat history',
        description: 'Analyze existing chat history to identify potential topics for testing'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Topics extracted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Authentication required' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "extractTopicsFromHistory", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("debug-topics"),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug topics storage',
        description: 'Show raw chat history data to debug topic storage'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Debug data retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Authentication required' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "debugTopics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)("chat-streak"),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getChatStreak", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, common_1.Controller)('chat'),
    __metadata("design:paramtypes", [chat_service_1.ChatService])
], ChatController);
//# sourceMappingURL=chat.controller.js.map