"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDate = exports.formatTimestampToIOS = void 0;
const formatTimestampToIOS = (timestamp) => {
    if (!timestamp) {
        return null;
    }
    try {
        const parsedTimestamp = parseInt(timestamp, 10);
        if (isNaN(parsedTimestamp)) {
            return timestamp;
        }
        const date = new Date(parsedTimestamp);
        return date.toISOString();
    }
    catch (error) {
        return timestamp;
    }
};
exports.formatTimestampToIOS = formatTimestampToIOS;
const formatDate = (dateString) => {
    try {
        const timezoneMatch = dateString.match(/GMT([+-]\d{4})/);
        if (timezoneMatch) {
            const timezoneOffset = timezoneMatch[1];
            dateString = dateString.replace(" " + dateString.split(" ").pop(), "");
            const dateObj = new Date(dateString);
            if (isNaN(dateObj.getTime())) {
                console.error("Invalid date value after parsing.");
                return null;
            }
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        else {
            console.error("Timezone offset not found in date string.");
            return null;
        }
    }
    catch (error) {
        console.error(`Invalid date format: ${dateString}. Error:`, error);
        return null;
    }
};
exports.formatDate = formatDate;
//# sourceMappingURL=date_formatter.js.map