"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminAnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const admin_guard_1 = require("../guard/admin.guard");
const swagger_1 = require("@nestjs/swagger");
const admin_analytics_service_1 = require("./admin-analytics.service");
let AdminAnalyticsController = class AdminAnalyticsController {
    constructor(adminAnalyticsService) {
        this.adminAnalyticsService = adminAnalyticsService;
    }
    async getStudentAnalytics(userId) {
        return this.adminAnalyticsService.getStudentAnalytics(userId);
    }
    async downloadStudentReport(userId, format = 'pdf') {
        return this.adminAnalyticsService.generateStudentReport(userId, format);
    }
    async getStudentActivityChart(userId, period = 'month') {
        return this.adminAnalyticsService.getStudentActivityChart(userId, period);
    }
};
exports.AdminAnalyticsController = AdminAnalyticsController;
__decorate([
    (0, common_1.Get)('student/:userId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get comprehensive student analytics (Admin only)',
        description: 'Get detailed analytics for a specific student including quiz stats, chat history, leaderboard position, and activity patterns'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Student analytics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                studentInfo: {
                    type: 'object',
                    properties: {
                        userId: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        phone: { type: 'string' },
                        class: { type: 'string' },
                        schoolName: { type: 'string' },
                        profileImage: { type: 'string' },
                        createdAt: { type: 'string' },
                        subjects: { type: 'array', items: { type: 'string' } }
                    }
                },
                analytics: {
                    type: 'object',
                    properties: {
                        quizStats: {
                            type: 'object',
                            properties: {
                                totalAttempted: { type: 'number' },
                                accuracy: { type: 'number' },
                                subjectWiseAttempts: { type: 'object' },
                                averageScores: { type: 'object' },
                                lastQuizDate: { type: 'string' },
                                topicsCompleted: { type: 'number' }
                            }
                        },
                        chatStats: {
                            type: 'object',
                            properties: {
                                totalMessages: { type: 'number' },
                                totalDoubts: { type: 'number' },
                                mostDiscussedSubject: { type: 'string' },
                                totalTimeSpent: { type: 'string' },
                                timeOfDayMostActive: { type: 'string' },
                                streak: { type: 'number' }
                            }
                        },
                        leaderboardStats: {
                            type: 'object',
                            properties: {
                                currentRank: { type: 'number' },
                                sparkPoints: { type: 'number' },
                                rankMovement: { type: 'string' },
                                motivationLevel: { type: 'string' }
                            }
                        },
                        activityPattern: {
                            type: 'object',
                            properties: {
                                dailyActivity: { type: 'array' },
                                weeklyPattern: { type: 'object' },
                                monthlyTrend: { type: 'array' }
                            }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Student not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Admin access required' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminAnalyticsController.prototype, "getStudentAnalytics", null);
__decorate([
    (0, common_1.Get)('student/:userId/download'),
    (0, swagger_1.ApiOperation)({
        summary: 'Download student analytics as PDF (Admin only)',
        description: 'Generate and download a comprehensive PDF report of student analytics'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'PDF report generated successfully',
        headers: {
            'Content-Type': { description: 'application/pdf' },
            'Content-Disposition': { description: 'attachment; filename="student-report.pdf"' }
        }
    }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AdminAnalyticsController.prototype, "downloadStudentReport", null);
__decorate([
    (0, common_1.Get)('student/:userId/activity-chart'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get student activity chart data (Admin only)',
        description: 'Get chart data for student activity visualization'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Chart data retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                dailyActivity: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            date: { type: 'string' },
                            queries: { type: 'number' },
                            timeSpent: { type: 'number' },
                            subjects: { type: 'array', items: { type: 'string' } }
                        }
                    }
                },
                subjectDistribution: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            subject: { type: 'string' },
                            percentage: { type: 'number' },
                            queries: { type: 'number' }
                        }
                    }
                },
                performanceTrend: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            date: { type: 'string' },
                            accuracy: { type: 'number' },
                            rank: { type: 'number' }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('period')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AdminAnalyticsController.prototype, "getStudentActivityChart", null);
exports.AdminAnalyticsController = AdminAnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Admin Analytics'),
    (0, common_1.Controller)('admin/analytics'),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [admin_analytics_service_1.AdminAnalyticsService])
], AdminAnalyticsController);
//# sourceMappingURL=admin-analytics.controller.js.map