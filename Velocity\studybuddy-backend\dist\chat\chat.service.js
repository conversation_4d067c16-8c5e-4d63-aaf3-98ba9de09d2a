"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const openai_1 = require("openai");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
const users_service_1 = require("../users/users.service");
const date_formatter_1 = require("../utils/date_formatter");
let ChatService = class ChatService {
    constructor(chatHistoryModel) {
        this.chatHistoryModel = chatHistoryModel;
        this.openai = new openai_1.OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    }
    isPreviousQuery(query) {
        const q = query.toLowerCase();
        return q.includes('previous') || q.includes('last') || q.includes('before') || q.includes('earlier');
    }
    async getChatResponse(userID, subject, query, topic) {
        let userClass = (await this.usersService.getUserDetailsByUserId(userID))["class"];
        const lastTwoChats = await this.chatHistoryModel
            .find({ userId: userID })
            .sort({ createdAt: -1 })
            .limit(2)
            .exec();
        if (!this.isEducationalContent(query)) {
            throw new common_1.BadRequestException('Query must be related to educational content.');
        }
        if (this.isPreviousQuery(query)) {
            const lastChat = lastTwoChats.find((chat) => chat.subjectWise?.some((s) => s.subject === subject &&
                s.queries?.length > 0 &&
                s.queries[s.queries.length - 1].query.toLowerCase() !== query.toLowerCase()));
            const subjectWise = lastChat?.subjectWise?.find((s) => s.subject === subject);
            const lastValidQuery = subjectWise?.queries?.slice(-1)[0];
            if (!lastValidQuery) {
                return "I couldn't find a previous educational question to elaborate on.";
            }
            const followUpPrompt = `This is a follow-up to a previous question asked by the student.

Previous Question: ${lastValidQuery.query}
Previous Answer: ${lastValidQuery.response}
New Request: ${query}

Please elaborate on the previous topic and give a clearer or deeper explanation. At the end, include a <summary>Q: ... A: ...</summary>`;
            const response = await this.openai.chat.completions.create({
                model: 'gpt-4o-mini',
                messages: [
                    {
                        role: 'system',
                        content: `You are an educational assistant providing clear and age-appropriate explanations for students up to PUC level. Respond based on the student's previous question and your earlier answer.`,
                    },
                    {
                        role: 'user',
                        content: followUpPrompt,
                    },
                ],
                temperature: 0.2,
            });
            const reply = response.choices[0]?.message;
            const totalTokens = response.usage.total_tokens;
            if (!reply || !reply.content) {
                throw new common_1.BadRequestException('No response received from OpenAI.');
            }
            const summaryMatch = reply.content.match(/<summary>(.*?)<\/summary>/);
            const summary = summaryMatch ? summaryMatch[1].trim() : '';
            const cleanResponse = reply.content.replace(/<summary>.*?<\/summary>/s, '').trim();
            await this.addQueryResponse(userID, query, cleanResponse, totalTokens, subject, summary, topic);
            return cleanResponse;
        }
        const filteredPreviousContext = lastTwoChats
            .flatMap(chat => chat.subjectWise
            .filter(subjectWise => subjectWise.subject === subject && (!topic || subjectWise.queries.some(q => q.query.toLowerCase().includes(topic.toLowerCase()) || q.response.toLowerCase().includes(topic.toLowerCase()))))
            .flatMap(subjectWise => subjectWise.queries
            .filter(q => q.summary)
            .map(q => q.summary)))
            .join('\n');
        const prompt = `You are a friendly and supportive educational assistant for students in grades 6 to 12. Always answer based on the student's class, the selected subject, and the specific topic if provided. If the question is not related to the subject or topic, or is inappropriate, gently guide the student back to academic topics with kindness and encouragement. Never provide explicit or inappropriate content, but do not scold—be positive and helpful.

IMPORTANT: Only answer questions about the current subject: ${subject}${topic ? ` and topic: ${topic}` : ''}. If the question is about something else, politely say you can only answer about this subject and topic, and encourage the student to ask relevant questions.

Student Grade: ${userClass}
Subject: ${subject}${topic ? `\nTopic: ${topic}` : ''}
Previous Context: ${filteredPreviousContext}
Student's Question: ${query}`;
        const response = await this.openai.chat.completions.create({
            model: 'gpt-4o-mini',
            messages: [
                {
                    role: 'system',
                    content: `You are a friendly, supportive educational assistant for students in grades 6 to 12. Your job is to:
- Answer questions based on the student's grade, the selected subject, and the specific topic (if provided).
- If a question is vulgar, inappropriate, or unrelated to the subject/topic, respond kindly and encourage the student to ask academic questions. For example, say: "I'm here to help you learn about ${subject}${topic ? `, especially the topic '${topic}'` : ''}. Let's focus on your studies!"
- Never provide explicit, adult, or inappropriate content. If asked, gently redirect the conversation to academic topics.
- Use clear, age-appropriate language and encourage curiosity.
- IMPORTANT: If the question is about a different subject or topic, politely inform the student that you can only answer about '${subject}'${topic ? ` and the topic '${topic}'` : ''}, and encourage them to ask relevant questions.
- At the end of each response, include a concise summary in the format: <summary>Q: [Student's Question] A: [One-line summary of your response]</summary> (for internal use only, not shown to the student).`
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            temperature: 0.2,
        });
        const reply = response.choices[0]?.message;
        const totalTokens = response.usage.total_tokens;
        if (!reply) {
            throw new common_1.BadRequestException('No response received from OpenAI.');
        }
        const summaryMatch = reply.content.match(/<summary>(.*?)<\/summary>/);
        const summary = summaryMatch ? summaryMatch[1] : '';
        const cleanResponse = reply.content.replace(/<summary>.*?<\/summary>/s, '').trim();
        await this.addQueryResponse(userID, query, cleanResponse, totalTokens, subject, summary);
        return cleanResponse;
    }
    isEducationalContent(query) {
        const bannedWords = ["adult", "inappropriate", "explicit", "sex"];
        const lowerCaseQuery = query.toLowerCase();
        if (this.isPreviousQuery(query))
            return true;
        return !bannedWords.some((word) => lowerCaseQuery.includes(word));
    }
    async addQueryResponse(userId, query, response, tokensUsed, subject, summary, topic) {
        const today = (0, date_formatter_1.formatDate)((0, date_formatter_1.formatTimestampToIOS)(String(new Date())));
        const chatHistory = await this.chatHistoryModel.findOne({ userId, date: today });
        if (chatHistory) {
            let subjectWise = chatHistory.subjectWise.find((s) => s.subject === subject);
            if (!subjectWise) {
                subjectWise = { subject, queries: [] };
                chatHistory.subjectWise.push(subjectWise);
                if (!chatHistory.subjects.includes(subject)) {
                    chatHistory.subjects.push(subject);
                }
            }
            subjectWise.queries.push({ query, response, tokensUsed, summary, topic });
            chatHistory.totalTokensSpent += tokensUsed;
            if (topic && !chatHistory.topics.includes(topic)) {
                chatHistory.topics.push(topic);
            }
            await chatHistory.save();
        }
        else {
            await this.chatHistoryModel.create({
                userId,
                date: today,
                subjectWise: [{ subject, queries: [{ query, response, tokensUsed, summary, topic }] }],
                totalTokensSpent: tokensUsed,
                subjects: [subject],
                topics: topic ? [topic] : [],
            });
        }
    }
    async getHistoryForDay(userId, date) {
        const targetDate = (0, date_formatter_1.formatDate)((0, date_formatter_1.formatTimestampToIOS)(String(new Date(date))));
        let result = await this.chatHistoryModel.findOne({ userId: userId, date: targetDate }).exec();
        if (result) {
            return { data: result };
        }
        else {
            throw new common_1.NotFoundException("History not found");
        }
    }
    async getHistoryofLastFive(userId) {
        let result = await this.chatHistoryModel.find({ userId: userId }).exec();
        if (result) {
            return { data: result };
        }
        else {
            throw new common_1.NotFoundException("History not found");
        }
    }
    async getChatStreak(userId) {
        try {
            const chatHistories = await this.chatHistoryModel.find({ userId: userId }).exec();
            const streak = chatHistories.length;
            return { streak: streak };
        }
        catch (error) {
            return { streak: 0 };
        }
    }
    async getFilteredChatHistory(userId, lowerBoundDate, upperBoundDate) {
        const startOfLowerBound = (0, date_formatter_1.formatDate)((0, date_formatter_1.formatTimestampToIOS)(String(new Date(lowerBoundDate))));
        const startOfUpperBound = (0, date_formatter_1.formatDate)((0, date_formatter_1.formatTimestampToIOS)(String(new Date(upperBoundDate))));
        return this.chatHistoryModel.find({
            userId,
            date: { $gte: startOfLowerBound, $lte: startOfUpperBound },
        }, {
            subjects: 1,
            date: 1,
            _id: 0,
        }).exec();
    }
    async getRecentTopics(userId) {
        const result = await this.chatHistoryModel.find({ userId: userId }).exec();
        if (result) {
            const allTopics = result.reduce((acc, item) => {
                const itemTopics = (item?.topics || []).map(topic => String(topic));
                const queryTopics = item.subjectWise?.flatMap(subjectData => subjectData.queries
                    .filter(query => query.topic)
                    .map(query => String(query.topic))) || [];
                return acc.concat(itemTopics, queryTopics);
            }, []);
            const uniqueTopics = [...new Set(allTopics)].filter(topic => topic && topic.trim() !== '');
            return { data: uniqueTopics };
        }
        else {
            return { data: [] };
        }
    }
    async getTopicChatHistory(userId, topic) {
        const result = await this.chatHistoryModel.find({ userId: userId }).exec();
        if (result) {
            const topicHistory = result.map(chatHistory => ({
                ...chatHistory.toObject(),
                subjectWise: chatHistory.subjectWise.map(subjectData => ({
                    ...subjectData,
                    queries: subjectData.queries.filter(query => {
                        if (query.topic === topic)
                            return true;
                        const topicLower = topic.toLowerCase();
                        const queryLower = query.query.toLowerCase();
                        const responseLower = query.response.toLowerCase();
                        return queryLower.includes(topicLower) ||
                            responseLower.includes(topicLower) ||
                            this.isTopicRelated(query.query, topic) ||
                            this.isTopicRelated(query.response, topic);
                    })
                })).filter(subjectData => subjectData.queries.length > 0)
            })).filter(chatHistory => chatHistory.subjectWise.length > 0);
            return { data: topicHistory };
        }
        else {
            return { data: [] };
        }
    }
    isTopicRelated(text, topic) {
        const textWords = text.toLowerCase().split(/\s+/);
        const topicWords = topic.toLowerCase().split(/\s+/);
        return topicWords.some(topicWord => textWords.some(textWord => textWord.includes(topicWord) || topicWord.includes(textWord)));
    }
    async extractTopicsFromHistory(userId) {
        const result = await this.chatHistoryModel.find({ userId: userId }).exec();
        if (result) {
            const potentialTopics = new Set();
            result.forEach(chatHistory => {
                chatHistory.subjectWise?.forEach(subjectData => {
                    subjectData.queries.forEach(query => {
                        if (query.summary) {
                            const summaryWords = query.summary.toLowerCase();
                            if (summaryWords.includes('addition'))
                                potentialTopics.add('Addition');
                            if (summaryWords.includes('subtraction'))
                                potentialTopics.add('Subtraction');
                            if (summaryWords.includes('multiplication'))
                                potentialTopics.add('Multiplication');
                            if (summaryWords.includes('division'))
                                potentialTopics.add('Division');
                            if (summaryWords.includes('algebra'))
                                potentialTopics.add('Algebra');
                            if (summaryWords.includes('geometry'))
                                potentialTopics.add('Geometry');
                            if (summaryWords.includes('physics'))
                                potentialTopics.add('Physics');
                            if (summaryWords.includes('chemistry'))
                                potentialTopics.add('Chemistry');
                            if (summaryWords.includes('biology'))
                                potentialTopics.add('Biology');
                            if (summaryWords.includes('quiz'))
                                potentialTopics.add('Quiz Questions');
                        }
                        const queryContent = query.query.toLowerCase();
                        if (queryContent.includes('addition') || queryContent.includes('add'))
                            potentialTopics.add('Addition');
                        if (queryContent.includes('quiz'))
                            potentialTopics.add('Quiz Questions');
                    });
                });
            });
            return { data: Array.from(potentialTopics) };
        }
        else {
            return { data: [] };
        }
    }
};
exports.ChatService = ChatService;
__decorate([
    (0, common_1.Inject)(users_service_1.UsersService),
    __metadata("design:type", users_service_1.UsersService)
], ChatService.prototype, "usersService", void 0);
exports.ChatService = ChatService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(chatHistory_schema_1.ChatHistory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ChatService);
//# sourceMappingURL=chat.service.js.map