"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
let AdminGuard = class AdminGuard {
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new common_1.ForbiddenException('Authorization token missing or invalid');
        }
        const token = authHeader.split(' ')[1];
        try {
            const payload = this.jwtService.verify(token, { secret: process.env.JWT_PRIVATE_KEY });
            if (payload.role !== 'admin') {
                throw new common_1.ForbiddenException(`Access restricted to admin users. Current role: ${payload.role || 'none'}`);
            }
            return true;
        }
        catch (error) {
            if (error.name === 'JsonWebTokenError') {
                throw new common_1.ForbiddenException('Invalid token format');
            }
            else if (error.name === 'TokenExpiredError') {
                throw new common_1.ForbiddenException('Token has expired');
            }
            else if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            else {
                throw new common_1.ForbiddenException('Token verification failed');
            }
        }
    }
};
exports.AdminGuard = AdminGuard;
exports.AdminGuard = AdminGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], AdminGuard);
//# sourceMappingURL=admin.guard.js.map