"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/ChatHeader */ \"(app-pages-browser)/./src/components/layout/ChatHeader.tsx\");\n/* harmony import */ var _components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/ChatInput */ \"(app-pages-browser)/./src/components/layout/ChatInput.tsx\");\n/* harmony import */ var _components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/SidebarContent */ \"(app-pages-browser)/./src/components/layout/SidebarContent.tsx\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var _components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/autoScrollChatArea */ \"(app-pages-browser)/./src/components/ui/autoScrollChatArea.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/raise-issue-modal */ \"(app-pages-browser)/./src/components/ui/raise-issue-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// import {subjectOptions} from '@/lib/utils'\n// import SubjectDialog from \"@/components/ui/subjectSectionDialog\";\n\n\n\n\nfunction ChatInterfaceContent() {\n    _s();\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subjectName, setSubjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [topicName, setTopicName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [topicHistory, setTopicHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRaiseIssueModal, setShowRaiseIssueModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Function to replace subject IDs with subject names in responses\n    const replaceSubjectIdsInResponse = (response)=>{\n        if (!subjectName || !selectedSubject) return response;\n        // Replace subject ID with subject name in the response\n        const subjectIdPattern = new RegExp(selectedSubject, 'g');\n        return response.replace(subjectIdPattern, subjectName);\n    };\n    const fetchChatHistory = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"\".concat(\"http://localhost:3000/api\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            const responseData = await response.json();\n            const historyData = Array.isArray(responseData.data) ? responseData.data : responseData.data ? [\n                responseData.data\n            ] : [];\n            setChatHistory(historyData);\n            // Process the history data for the current session\n            const sessionData = {};\n            historyData.forEach((item)=>{\n                var _item_subjectWise;\n                (_item_subjectWise = item.subjectWise) === null || _item_subjectWise === void 0 ? void 0 : _item_subjectWise.forEach((subjectData)=>{\n                    if (!sessionData[subjectData.subject]) {\n                        sessionData[subjectData.subject] = [];\n                    }\n                    sessionData[subjectData.subject] = [\n                        ...sessionData[subjectData.subject],\n                        ...subjectData.queries\n                    ];\n                });\n            });\n            setCurrentSession(sessionData);\n        } catch (error) {\n            console.error(\"Error fetching chat history:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterfaceContent.useEffect\": ()=>{\n            fetchChatHistory();\n        }\n    }[\"ChatInterfaceContent.useEffect\"], []) // fetchChatHistory is stable, no need to add as dependency;\n    ;\n    // Handle subject and topic parameters from URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterfaceContent.useEffect\": ()=>{\n            const subjectFromUrl = searchParams.get('subject');\n            const topicFromUrl = searchParams.get('topic');\n            const subjectNameFromUrl = searchParams.get('subjectName');\n            const topicNameFromUrl = searchParams.get('topicName');\n            if (subjectFromUrl) {\n                setSelectedSubject(subjectFromUrl);\n                setShowDashboard(false);\n            } else {\n                // Only show dashboard if no subject parameter is present\n                setShowDashboard(true);\n            }\n            if (topicFromUrl) {\n                setSelectedTopic(topicFromUrl);\n            }\n            if (subjectNameFromUrl) {\n                setSubjectName(decodeURIComponent(subjectNameFromUrl));\n            }\n            if (topicNameFromUrl) {\n                setTopicName(decodeURIComponent(topicNameFromUrl));\n            }\n        }\n    }[\"ChatInterfaceContent.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to dashboard if no subject is selected and we should show dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterfaceContent.useEffect\": ()=>{\n            // Add a small delay to ensure URL parameters are processed first\n            const timer = setTimeout({\n                \"ChatInterfaceContent.useEffect.timer\": ()=>{\n                    if (showDashboard && !selectedSubject && !searchParams.get('subject')) {\n                        router.push('/dashboard');\n                    }\n                }\n            }[\"ChatInterfaceContent.useEffect.timer\"], 100);\n            return ({\n                \"ChatInterfaceContent.useEffect\": ()=>clearTimeout(timer)\n            })[\"ChatInterfaceContent.useEffect\"];\n        }\n    }[\"ChatInterfaceContent.useEffect\"], [\n        showDashboard,\n        selectedSubject,\n        router,\n        searchParams\n    ]);\n    // Rest of the component remains the same...\n    const handleSubjectSelect = (subject)=>{\n        setSelectedSubject(subject);\n        setShowDashboard(false);\n        if (currentSession[subject]) {\n            const subjectMessages = currentSession[subject].flatMap((query)=>[\n                    {\n                        content: query.query,\n                        isUser: true,\n                        lastMessage: false\n                    },\n                    {\n                        content: query.response,\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n            setMessages(subjectMessages);\n        } else {\n            setMessages([]);\n        }\n    };\n    const handleNewSession = ()=>{\n        // Navigate to dashboard instead of showing dialog\n        router.push('/dashboard');\n    };\n    const handleTopicSelect = async (topic)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"\".concat(\"http://localhost:3000/api\", \"/chat/topic-history?topic=\").concat(encodeURIComponent(topic)), {\n                headers: getAuthHeaders()\n            });\n            const responseData = await response.json();\n            const historyData = Array.isArray(responseData.data) ? responseData.data : [];\n            setTopicHistory(historyData);\n            // Process the topic history data for the current session\n            const sessionData = {};\n            historyData.forEach((item)=>{\n                var _item_subjectWise;\n                (_item_subjectWise = item.subjectWise) === null || _item_subjectWise === void 0 ? void 0 : _item_subjectWise.forEach((subjectData)=>{\n                    if (!sessionData[subjectData.subject]) {\n                        sessionData[subjectData.subject] = [];\n                    }\n                    sessionData[subjectData.subject] = [\n                        ...sessionData[subjectData.subject],\n                        ...subjectData.queries\n                    ];\n                });\n            });\n            // If there's topic history, load the first subject's messages\n            const firstSubject = Object.keys(sessionData)[0];\n            if (firstSubject) {\n                setSelectedSubject(firstSubject);\n                setSelectedTopic(topic);\n                setTopicName(topic);\n                setShowDashboard(false);\n                const topicMessages = sessionData[firstSubject].flatMap((query)=>[\n                        {\n                            content: query.query,\n                            isUser: true,\n                            lastMessage: false\n                        },\n                        {\n                            content: query.response,\n                            isUser: false,\n                            lastMessage: false\n                        }\n                    ]);\n                setMessages(topicMessages);\n                setCurrentSession(sessionData);\n            }\n        } catch (error) {\n            console.error(\"Error fetching topic history:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSendMessage = async (message)=>{\n        if (!selectedSubject || !message.trim()) return;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    content: message,\n                    isUser: true,\n                    lastMessage: false\n                }\n            ]);\n        setIsTyping(true);\n        try {\n            const topicParam = topicName ? \"&topic=\".concat(encodeURIComponent(topicName)) : '';\n            // Debug logging\n            console.log('Sending chat request:', {\n                selectedSubject,\n                topicName,\n                topicParam,\n                message: message.substring(0, 50) + '...'\n            });\n            const response = await fetch(\"\".concat(\"http://localhost:3000/api\", \"/chat?subject=\").concat(selectedSubject, \"&query=\").concat(encodeURIComponent(message)).concat(topicParam), {\n                headers: getAuthHeaders()\n            });\n            const data = await response.json();\n            // Replace subject IDs with subject names in the response\n            const processedResponse = replaceSubjectIdsInResponse(data.response);\n            const newQuery = {\n                query: message,\n                response: processedResponse,\n                tokensUsed: data.tokensUsed || 0,\n                // lastMessage: true,\n                _id: Date.now().toString(),\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: processedResponse,\n                        isUser: false,\n                        lastMessage: true\n                    }\n                ]);\n            setIsTyping(false);\n            // Ensure lastMessage is updated only for the latest query\n            setCurrentSession((prev)=>{\n                const updatedQueries = [\n                    ...prev[selectedSubject] || [],\n                    newQuery\n                ];\n                return {\n                    ...prev,\n                    [selectedSubject]: updatedQueries\n                };\n            });\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: \"Sorry, there was an error processing your request.\",\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n        }\n    };\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function to scroll to bottom\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current;\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterfaceContent.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterfaceContent.useEffect\"], [\n        messages\n    ]);\n    // //console.log(messages,'messages-chat')\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen p-2 text-black bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__.SidebarContent, {\n                        onNewSession: handleNewSession,\n                        chatHistory: chatHistory,\n                        onSubjectSelect: handleSubjectSelect,\n                        onTopicSelect: handleTopicSelect,\n                        currentSubject: selectedSubject,\n                        currentTopic: selectedTopic,\n                        isLoading: isLoading,\n                        subjectName: subjectName,\n                        topicName: topicName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white rounded-lg border border-[#309CEC]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                                subjectName: subjectName,\n                                topicName: topicName,\n                                onRaiseIssue: ()=>setShowRaiseIssueModal(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                messages: messages,\n                                isTyping: isTyping\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__.ChatInput, {\n                                onSendMessage: handleSendMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showRaiseIssueModal,\n                onClose: ()=>setShowRaiseIssueModal(false),\n                currentSubject: subjectName,\n                currentTopic: topicName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterfaceContent, \"NzzRlxRF4Kn7Ri5yM8aLdIi4K18=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams\n    ];\n});\n_c = ChatInterfaceContent;\nfunction ChatInterface() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInterfaceContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChatInterface;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatInterfaceContent\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});