"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizAttemptSchema = exports.QuizAttempt = exports.QuizAnswerSchema = exports.QuizAnswer = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let QuizAnswer = class QuizAnswer extends mongoose_2.Document {
};
exports.QuizAnswer = QuizAnswer;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Schema.Types.ObjectId, ref: 'Quiz' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuizAnswer.prototype, "quizId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuizAnswer.prototype, "selectedAnswer", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Boolean)
], QuizAnswer.prototype, "isCorrect", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Number)
], QuizAnswer.prototype, "timeSpent", void 0);
exports.QuizAnswer = QuizAnswer = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], QuizAnswer);
exports.QuizAnswerSchema = mongoose_1.SchemaFactory.createForClass(QuizAnswer);
let QuizAttempt = class QuizAttempt extends mongoose_2.Document {
};
exports.QuizAttempt = QuizAttempt;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Schema.Types.ObjectId, ref: 'User' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuizAttempt.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Schema.Types.ObjectId, ref: 'Subject' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuizAttempt.prototype, "subjectId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], QuizAttempt.prototype, "topicId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [exports.QuizAnswerSchema], required: true }),
    __metadata("design:type", Array)
], QuizAttempt.prototype, "answers", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "totalQuestions", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "correctAnswers", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "score", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "totalTimeSpent", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'completed' }),
    __metadata("design:type", String)
], QuizAttempt.prototype, "status", void 0);
exports.QuizAttempt = QuizAttempt = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], QuizAttempt);
exports.QuizAttemptSchema = mongoose_1.SchemaFactory.createForClass(QuizAttempt);
//# sourceMappingURL=quiz-attempt.schema.js.map