import { HydratedDocument, Types, Model } from 'mongoose';
import * as mongoose from 'mongoose';
export type UserDocument = HydratedDocument<User>;
export declare class User {
    email: string;
    password: string;
    role: string;
    userDetails: Types.ObjectId;
}
declare const UserSchema: mongoose.Schema<User, Model<User, any, any, any, mongoose.Document<unknown, any, User> & User & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, User, mongoose.Document<unknown, {}, mongoose.FlatRecord<User>> & mongoose.FlatRecord<User> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export { UserSchema };
