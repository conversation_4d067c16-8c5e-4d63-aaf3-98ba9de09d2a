import { Model } from 'mongoose';
import { ChatHistory, ChatHistoryDocument } from 'src/schemas/chatHistory.schema';
export declare class ChatService {
    private chatHistoryModel;
    private readonly openai;
    private readonly usersService;
    constructor(chatHistoryModel: Model<ChatHistoryDocument>);
    private isPreviousQuery;
    getChatResponse(userID: string, subject: string, query: string, topic?: string): Promise<string>;
    private isEducationalContent;
    addQueryResponse(userId: string, query: string, response: string, tokensUsed: number, subject: string, summary: string): Promise<void>;
    getHistoryForDay(userId: string, date: string): Promise<{
        data: import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>;
    }>;
    getHistoryofLastFive(userId: string): Promise<{
        data: (import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>)[];
    }>;
    getChatStreak(userId: string): Promise<{
        streak: number;
    }>;
    getFilteredChatHistory(userId: string, lowerBoundDate: string, upperBoundDate: string): Promise<(import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    } & Required<{
        _id: import("mongoose").Types.ObjectId;
    }>)[]>;
}
