import { Model } from 'mongoose';
import { ChatHistory, ChatHistoryDocument } from 'src/schemas/chatHistory.schema';
export declare class ChatService {
    private chatHistoryModel;
    private readonly openai;
    private readonly usersService;
    constructor(chatHistoryModel: Model<ChatHistoryDocument>);
    private isPreviousQuery;
    getChatResponse(userID: string, subject: string, query: string, topic?: string): Promise<string>;
    private isEducationalContent;
    addQueryResponse(userId: string, query: string, response: string, tokensUsed: number, subject: string, summary: string, topic?: string): Promise<void>;
    getHistoryForDay(userId: string, date: string): Promise<{
        data: import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>;
    }>;
    getHistoryofLastFive(userId: string): Promise<{
        data: (import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>)[];
    }>;
    getChatStreak(userId: string): Promise<{
        streak: number;
    }>;
    getFilteredChatHistory(userId: string, lowerBoundDate: string, upperBoundDate: string): Promise<(import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }> & import("mongoose").Document<unknown, {}, ChatHistory> & ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    } & Required<{
        _id: import("mongoose").Types.ObjectId;
    }>)[]>;
    getRecentTopics(userId: string): Promise<{
        data: string[];
    }>;
    getTopicChatHistory(userId: string, topic: string): Promise<{
        data: {
            subjectWise: {
                queries: import("src/schemas/chatHistory.schema").QueryResponse[];
                subject: string;
            }[];
            _id: import("mongoose").Types.ObjectId;
            $locals: Record<string, unknown>;
            $op: "save" | "validate" | "remove" | null;
            $where: Record<string, unknown>;
            baseModelName?: string;
            collection: import("mongoose").Collection;
            db: import("mongoose").Connection;
            errors?: import("mongoose").Error.ValidationError;
            id?: any;
            isNew: boolean;
            schema: import("mongoose").Schema;
            userId: import("mongoose").Types.ObjectId;
            date: String;
            totalTokensSpent: number;
            subjects: String[];
            topics: String[];
            __v: number;
        }[];
    }>;
    private isTopicRelated;
    extractTopicsFromHistory(userId: string): Promise<{
        data: string[];
    }>;
}
