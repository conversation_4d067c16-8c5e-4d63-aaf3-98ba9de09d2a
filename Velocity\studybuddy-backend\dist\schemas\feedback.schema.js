"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackSchema = exports.Feedback = exports.FeedbackStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var FeedbackStatus;
(function (FeedbackStatus) {
    FeedbackStatus["PENDING"] = "pending";
    FeedbackStatus["COMPLETED"] = "completed";
    FeedbackStatus["REJECTED"] = "rejected";
    FeedbackStatus["OPEN"] = "open";
    FeedbackStatus["IN_PROGRESS"] = "in-progress";
    FeedbackStatus["RESOLVED"] = "resolved";
    FeedbackStatus["CLOSED"] = "closed";
})(FeedbackStatus || (exports.FeedbackStatus = FeedbackStatus = {}));
let Feedback = class Feedback extends mongoose_2.Document {
};
exports.Feedback = Feedback;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Feedback.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Feedback.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Schema.Types.ObjectId }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Feedback.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Feedback.prototype, "userName", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        enum: Object.values(FeedbackStatus),
        default: FeedbackStatus.PENDING
    }),
    __metadata("design:type", String)
], Feedback.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String, default: null }),
    __metadata("design:type", String)
], Feedback.prototype, "adminResponse", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
    }),
    __metadata("design:type", String)
], Feedback.prototype, "priority", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String, default: 'General' }),
    __metadata("design:type", String)
], Feedback.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String, default: null }),
    __metadata("design:type", String)
], Feedback.prototype, "subject", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Feedback.prototype, "attachments", void 0);
exports.Feedback = Feedback = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Feedback);
exports.FeedbackSchema = mongoose_1.SchemaFactory.createForClass(Feedback);
//# sourceMappingURL=feedback.schema.js.map