export declare class TopicDto {
    name: string;
    description?: string;
}
export declare class SubjectDto {
    name: string;
    description?: string;
    topics?: TopicDto[];
}
export declare class UpdateSubjectDto extends SubjectDto {
}
export declare class AddTopicDto {
    subjectId: string;
    topic: TopicDto;
}
export declare class UpdateTopicDto {
    subjectId: string;
    topicId: string;
    topic: TopicDto;
}
