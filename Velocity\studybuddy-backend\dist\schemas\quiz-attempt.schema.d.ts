import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class QuizAnswer extends Document {
    quizId: MongooseSchema.Types.ObjectId;
    selectedAnswer: number;
    isCorrect: boolean;
    timeSpent: number;
}
export declare const QuizAnswerSchema: MongooseSchema<QuizAnswer, import("mongoose").Model<QuizAnswer, any, any, any, Document<unknown, any, QuizAnswer> & QuizAnswer & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, QuizAnswer, Document<unknown, {}, import("mongoose").FlatRecord<QuizAnswer>> & import("mongoose").FlatRecord<QuizAnswer> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class QuizAttempt extends Document {
    userId: MongooseSchema.Types.ObjectId;
    subjectId: MongooseSchema.Types.ObjectId;
    topicId: string;
    answers: QuizAnswer[];
    totalQuestions: number;
    correctAnswers: number;
    score: number;
    totalTimeSpent: number;
    status: string;
}
export declare const QuizAttemptSchema: MongooseSchema<QuizAttempt, import("mongoose").Model<QuizAttempt, any, any, any, Document<unknown, any, QuizAttempt> & QuizAttempt & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, QuizAttempt, Document<unknown, {}, import("mongoose").FlatRecord<QuizAttempt>> & import("mongoose").FlatRecord<QuizAttempt> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
