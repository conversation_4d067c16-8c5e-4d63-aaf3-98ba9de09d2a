"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/layout/SidebarContent.tsx":
/*!**************************************************!*\
  !*** ./src/components/layout/SidebarContent.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _quiz_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quiz-card */ \"(app-pages-browser)/./src/components/layout/quiz-card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SidebarContent(param) {\n    let { onNewSession, chatHistory = [], onSubjectSelect, onTopicSelect, currentSubject, currentTopic, isLoading, subjectName, topicName } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [, setUserStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [, setIsStreakLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subjectMap, setSubjectMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [subjectsLoading, setSubjectsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recentTopics, setRecentTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topicsLoading, setTopicsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Fetch subjects for mapping IDs to names\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarContent.useEffect\": ()=>{\n            const fetchSubjects = {\n                \"SidebarContent.useEffect.fetchSubjects\": async ()=>{\n                    try {\n                        setSubjectsLoading(true);\n                        const allSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n                        const mapping = {};\n                        allSubjects.forEach({\n                            \"SidebarContent.useEffect.fetchSubjects\": (subject)=>{\n                                mapping[subject._id] = subject;\n                            }\n                        }[\"SidebarContent.useEffect.fetchSubjects\"]);\n                        setSubjectMap(mapping);\n                    } catch (error) {\n                        console.error('Failed to fetch subjects:', error);\n                    } finally{\n                        setSubjectsLoading(false);\n                    }\n                }\n            }[\"SidebarContent.useEffect.fetchSubjects\"];\n            fetchSubjects();\n        }\n    }[\"SidebarContent.useEffect\"], []);\n    // Fetch recent topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarContent.useEffect\": ()=>{\n            const fetchRecentTopics = {\n                \"SidebarContent.useEffect.fetchRecentTopics\": async ()=>{\n                    try {\n                        setTopicsLoading(true);\n                        // Only get explicitly stored recent topics (not auto-extracted ones)\n                        const response = await fetch(\"\".concat(\"http://localhost:3000/api\", \"/chat/recent-topics\"), {\n                            headers: getAuthHeaders()\n                        });\n                        const responseData = await response.json();\n                        const topics = Array.isArray(responseData.data) ? responseData.data : [];\n                        setRecentTopics(topics);\n                    } catch (error) {\n                        console.error('Failed to fetch recent topics:', error);\n                        setRecentTopics([]);\n                    } finally{\n                        setTopicsLoading(false);\n                    }\n                }\n            }[\"SidebarContent.useEffect.fetchRecentTopics\"];\n            fetchRecentTopics();\n        }\n    }[\"SidebarContent.useEffect\"], []);\n    const subjects = isLoading || !Array.isArray(chatHistory) ? [] : [\n        ...new Set(chatHistory.reduce((acc, item)=>{\n            const itemSubjects = (item === null || item === void 0 ? void 0 : item.subjects) || [];\n            return acc.concat(itemSubjects);\n        }, []))\n    ];\n    // Function to get display name for subject\n    const getSubjectDisplayName = (subjectId)=>{\n        const subject = subjectMap[subjectId];\n        return subject ? subject.name : subjectId; // Fallback to ID if not found\n    };\n    const handleSubjectClick = (subject)=>{\n        onSubjectSelect(subject);\n        setIsOpen(false);\n    };\n    const handleNewSession = ()=>{\n        onNewSession();\n        setIsOpen(false);\n    };\n    const handleTopicClick = (topic)=>{\n        if (onTopicSelect) {\n            onTopicSelect(topic);\n        }\n        setIsOpen(false);\n    };\n    const fetchStreakData = async ()=>{\n        setIsStreakLoading(true);\n        try {\n            const token = localStorage.getItem('accessToken');\n            if (!token) {\n                setUserStreak({\n                    streak: 0\n                });\n                setIsStreakLoading(false);\n                return;\n            }\n            const response = await fetch(\"\".concat(\"http://localhost:3000/api\" || 0, \"/chat/chat-streak\"), {\n                method: 'GET',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) {\n                // For any error, just set streak to 0 instead of throwing\n                console.warn(\"Chat streak API returned \".concat(response.status, \", defaulting to 0\"));\n                setUserStreak({\n                    streak: 0\n                });\n            } else {\n                const data = await response.json();\n                setUserStreak(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user streak data:', error);\n            setUserStreak({\n                streak: 0\n            });\n        } finally{\n            setIsStreakLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarContent.useEffect\": ()=>{\n            fetchStreakData();\n        }\n    }[\"SidebarContent.useEffect\"], []); // fetchStreakData is stable, no need to add as dependency\n    const SidebarItems = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" py-4 bg-gray-50 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            src: \"/assets/logo/studubuddy-logo-new.png\",\n                            alt: \"StudyBuddy Logo\",\n                            width: 160,\n                            height: 40,\n                            className: \"h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 19\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: handleNewSession,\n                    className: \"w-full bg-[#309CEC] text-white text-[18px] font-bold py-2 rounded-[76px] hover:bg-[#309CEC]/80 transition-colors\",\n                    children: \"+ Start a New Session\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm text-gray-400\",\n                            children: \"Recent Topics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                            className: \"h-[250px] w-full pr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: topicsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-center py-4\",\n                                    children: \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this) : recentTopics.length > 0 ? recentTopics.map((topic, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full text-[16px] py-2 rounded-full transition-colors \".concat(currentTopic === topic ? 'bg-[#309CEC] text-[#F9F5FF]' : 'text-[#858585] bg-[#F9F5FF] hover:bg-[#F9F5FF]/70'),\n                                        onClick: ()=>handleTopicClick(topic),\n                                        children: topic\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-center py-4\",\n                                    children: \"No topics yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_quiz_card__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        currentSubject: currentSubject,\n                        currentTopic: currentTopic,\n                        subjectName: subjectName,\n                        topicName: topicName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n            lineNumber: 186,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.Sheet, {\n                    open: isOpen,\n                    onOpenChange: setIsOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"fixed top-4 left-4 z-50 w-16 h-16 p-0 hover:bg-[#4024B9]/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetContent, {\n                            side: \"left\",\n                            className: \"w-80 bg-white border-r border-[#309CEC] p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetHeader, {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetTitle, {\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItems, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block w-80 bg-white rounded-lg border border-[#309CEC] p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItems, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SidebarContent, \"QOKleLLzy2gYrgAlZMebluUN+ws=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = SidebarContent;\nvar _c;\n$RefreshReg$(_c, \"SidebarContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/SidebarContent.tsx\n"));

/***/ })

});