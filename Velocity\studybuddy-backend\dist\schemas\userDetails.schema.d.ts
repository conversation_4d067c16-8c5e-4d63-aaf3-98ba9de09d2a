import { HydratedDocument, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type UserDetailsDocument = HydratedDocument<UserDetails>;
export declare class UserDetails {
    user: Types.ObjectId;
    dob: string;
    name: string;
    phoneno: string;
    schoolName: string;
    class: string;
    subjects: string[];
    profileImage: string;
}
export declare const UserDetailsSchema: mongoose.Schema<UserDetails, mongoose.Model<UserDetails, any, any, any, mongoose.Document<unknown, any, UserDetails> & UserDetails & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, UserDetails, mongoose.Document<unknown, {}, mongoose.FlatRecord<UserDetails>> & mongoose.FlatRecord<UserDetails> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
