import { Model } from 'mongoose';
import { Feedback } from 'src/schemas/feedback.schema';
import { CreateFeedbackDto, UpdateFeedbackStatusDto, FeedbackFilterDto } from 'src/dtos/feedback.dto';
import { UsersService } from 'src/users/users.service';
export declare class FeedbackService {
    private feedbackModel;
    private readonly usersService;
    constructor(feedbackModel: Model<Feedback>, usersService: UsersService);
    createFeedback(userId: string, createFeedbackDto: CreateFeedbackDto): Promise<Feedback>;
    getUserFeedbacks(userId: string): Promise<Feedback[]>;
    getUserFeedbackById(userId: string, feedbackId: string): Promise<Feedback>;
    getAllFeedbacks(filterDto: FeedbackFilterDto): Promise<Feedback[]>;
    getFeedbackById(feedbackId: string): Promise<Feedback>;
    updateFeedbackStatus(feedbackId: string, updateFeedbackStatusDto: UpdateFeedbackStatusDto): Promise<Feedback>;
    deleteFeedback(feedbackId: string): Promise<{
        success: boolean;
    }>;
}
