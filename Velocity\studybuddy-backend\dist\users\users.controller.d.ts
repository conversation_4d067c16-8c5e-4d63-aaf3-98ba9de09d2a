import { UserDto } from 'src/dtos/user.dto';
import { UsersService } from './users.service';
import { CreateUserDetailsDto } from 'src/dtos/createUserDetailsDto';
import { UserDetails } from 'src/schemas/userDetails.schema';
import { User } from 'src/schemas/user.schema';
import { AdminService } from 'src/admin/admin.service';
import { Subject } from 'src/schemas/subject.schema';
import { QuizFilterDto } from 'src/dtos/quiz.dto';
export declare class UsersController {
    private readonly usersService;
    private readonly adminService;
    constructor(usersService: UsersService, adminService: AdminService);
    getAllUsers(): Promise<User[]>;
    updateUser(id: string, updateData: UserDto): Promise<User>;
    deleteUser(id: string): Promise<{
        success: boolean;
    }>;
    create(req: any, createUserDetailsDto: CreateUserDetailsDto): Promise<import("mongoose").Document<unknown, {}, UserDetails> & UserDetails & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
    getUserDetails(req: any): Promise<string>;
    updateUserDetails(req: any, updateDto: CreateUserDetailsDto, user: User): Promise<UserDetails>;
    getAllSubjectsForUsers(req: any): Promise<Subject[]>;
    getAllQuizzesForUsers(filterDto: QuizFilterDto): Promise<import("../schemas/quiz.schema").Quiz[]>;
    getSubjectByIdForUsers(id: string): Promise<Subject>;
}
