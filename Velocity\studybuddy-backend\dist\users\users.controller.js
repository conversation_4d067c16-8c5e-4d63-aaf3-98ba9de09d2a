"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const user_dto_1 = require("../dtos/user.dto");
const jwt_auth_guard_1 = require("../guard/jwt-auth.guard");
const users_service_1 = require("./users.service");
const admin_guard_1 = require("../guard/admin.guard");
const createUserDetailsDto_1 = require("../dtos/createUserDetailsDto");
const userDetails_schema_1 = require("../schemas/userDetails.schema");
const user_schema_1 = require("../schemas/user.schema");
const swagger_1 = require("@nestjs/swagger");
const admin_service_1 = require("../admin/admin.service");
const subject_schema_1 = require("../schemas/subject.schema");
const quiz_dto_1 = require("../dtos/quiz.dto");
let UsersController = class UsersController {
    constructor(usersService, adminService) {
        this.usersService = usersService;
        this.adminService = adminService;
    }
    async getAllUsers() {
        return this.usersService.getAllUsers();
    }
    async updateUser(id, updateData) {
        if (!id) {
            throw new Error('User ID is required to update a user.');
        }
        return this.usersService.updateUser(id, updateData);
    }
    async deleteUser(id) {
        return await this.usersService.deleteUser(id);
    }
    async create(req, createUserDetailsDto) {
        try {
            return await this.usersService.createUserDetails(req['userID'], createUserDetailsDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getUserDetails(req) {
        return this.usersService.getUserDetailsByUserId(req['userID']);
    }
    async updateUserDetails(req, updateDto, user) {
        return this.usersService.updateUserDetails(req['userID'], updateDto);
    }
    async getAllSubjectsForUsers(req) {
        return this.usersService.getSubjectsForUser(req['userID']);
    }
    async getAllQuizzesForUsers(filterDto) {
        return this.adminService.getAllQuizzes(filterDto);
    }
    async getSubjectByIdForUsers(id) {
        return this.adminService.getSubjectById(id);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of users', type: [user_dto_1.UserDto] }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Update existing user (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Updated user', type: user_dto_1.UserDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Query)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_dto_1.UserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateUser", null);
__decorate([
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a user (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, common_1.Delete)(),
    __param(0, (0, common_1.Query)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Create or overwrite user profile details' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User details created', type: userDetails_schema_1.UserDetails }),
    (0, common_1.Post)('user-details'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, createUserDetailsDto_1.CreateUserDetailsDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user profile details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User details', type: userDetails_schema_1.UserDetails }),
    (0, common_1.Get)('user-details'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserDetails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Update current user profile details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Updated user details', type: userDetails_schema_1.UserDetails }),
    (0, common_1.Put)('user-details'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, createUserDetailsDto_1.CreateUserDetailsDto, user_schema_1.User]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateUserDetails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all subjects for users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of subjects', type: [subject_schema_1.Subject] }),
    (0, common_1.Get)('subjects'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAllSubjectsForUsers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get quizzes for users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of quizzes' }),
    (0, common_1.Get)('quizzes'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [quiz_dto_1.QuizFilterDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAllQuizzesForUsers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get subject by ID for users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Subject details', type: subject_schema_1.Subject }),
    (0, common_1.Get)('subjects/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getSubjectByIdForUsers", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        admin_service_1.AdminService])
], UsersController);
//# sourceMappingURL=users.controller.js.map