import { LeaderboardService } from './leaderboard.service';
export declare class LeaderboardController {
    private readonly leaderboardService;
    constructor(leaderboardService: LeaderboardService);
    getLeaderboard(req: any, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string, limit?: string): Promise<import("./leaderboard.service").LeaderboardResponse>;
    getUserRank(req: any, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<import("./leaderboard.service").LeaderboardUser>;
    searchUsers(req: any, searchQuery: string, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<import("./leaderboard.service").LeaderboardResponse>;
    getTopPerformers(req: any, period?: 'weekly' | 'monthly' | 'all', subject?: string, classFilter?: string): Promise<{
        topThree: import("./leaderboard.service").LeaderboardUser[];
        currentUser?: import("./leaderboard.service").LeaderboardUser;
    }>;
}
