"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const mongoose_1 = require("@nestjs/mongoose");
const quiz_attempt_schema_1 = require("../schemas/quiz-attempt.schema");
const user_schema_1 = require("../schemas/user.schema");
const subject_schema_1 = require("../schemas/subject.schema");
async function createSampleQuizAttempts() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const quizAttemptModel = app.get((0, mongoose_1.getModelToken)(quiz_attempt_schema_1.QuizAttempt.name));
    const userModel = app.get((0, mongoose_1.getModelToken)(user_schema_1.User.name));
    const subjectModel = app.get((0, mongoose_1.getModelToken)(subject_schema_1.Subject.name));
    try {
        const users = await userModel.find({ role: 'student' }).limit(5).exec();
        const subjects = await subjectModel.find().exec();
        if (users.length === 0 || subjects.length === 0) {
            console.log('No users or subjects found. Please create some first.');
            return;
        }
        console.log(`Found ${users.length} users and ${subjects.length} subjects`);
        for (const user of users) {
            console.log(`Creating quiz attempts for user: ${user.email}`);
            const numAttempts = Math.floor(Math.random() * 3) + 3;
            for (let i = 0; i < numAttempts; i++) {
                const randomSubject = subjects[Math.floor(Math.random() * subjects.length)];
                if (!randomSubject.topics || randomSubject.topics.length === 0) {
                    console.log(`  Skipping ${randomSubject.name} - no topics available`);
                    continue;
                }
                const randomTopic = randomSubject.topics[Math.floor(Math.random() * randomSubject.topics.length)];
                const totalQuestions = Math.floor(Math.random() * 5) + 5;
                const correctAnswers = Math.floor(Math.random() * totalQuestions * 0.4) + Math.floor(totalQuestions * 0.4);
                const score = Math.round((correctAnswers / totalQuestions) * 100);
                const totalTimeSpent = Math.floor(Math.random() * 300) + 180;
                const answers = [];
                for (let q = 0; q < totalQuestions; q++) {
                    answers.push({
                        quizId: randomSubject._id,
                        selectedAnswer: Math.floor(Math.random() * 4),
                        isCorrect: q < correctAnswers,
                        timeSpent: Math.floor(Math.random() * 60) + 15
                    });
                }
                const quizAttempt = new quizAttemptModel({
                    userId: user._id,
                    subjectId: randomSubject._id,
                    topicId: randomTopic._id,
                    answers: answers,
                    totalQuestions: totalQuestions,
                    correctAnswers: correctAnswers,
                    score: score,
                    totalTimeSpent: totalTimeSpent,
                    status: 'completed'
                });
                await quizAttempt.save();
                console.log(`  Created quiz attempt: ${randomSubject.name} - ${score}% (${correctAnswers}/${totalQuestions})`);
            }
        }
        console.log('Sample quiz attempts created successfully!');
    }
    catch (error) {
        console.error('Error creating sample quiz attempts:', error);
    }
    finally {
        await app.close();
    }
}
createSampleQuizAttempts();
//# sourceMappingURL=create-sample-quiz-attempts.js.map