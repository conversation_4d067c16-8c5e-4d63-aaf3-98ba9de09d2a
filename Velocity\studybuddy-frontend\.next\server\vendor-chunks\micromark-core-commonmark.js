"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-core-commonmark";
exports.ids = ["vendor-chunks/micromark-core-commonmark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/attention.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attention: () => (/* binding */ attention)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = {...events[open][1].end}\n          const end = {...events[index][1].start}\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongSequence : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisSequence,\n            start,\n            end: {...events[open][1].end}\n          }\n          closingSequence = {\n            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongSequence : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisSequence,\n            start: {...events[index][1].start},\n            end\n          }\n          text = {\n            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strongText : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasisText,\n            start: {...events[open][1].end},\n            end: {...events[index][1].start}\n          }\n          group = {\n            type: use > 1 ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.strong : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.emphasis,\n            start: {...openingSequence.start},\n            end: {...closingSequence.end}\n          }\n\n          events[open][1].end = {...openingSequence.start}\n          events[index][1].start = {...closingSequence.end}\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(\n            nextEvents,\n            (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(code)\n\n    // Always populated by defaults.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/autolink.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autolink: () => (/* binding */ autolink)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign) {\n      return nok(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&\n      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiControl)(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAtext)(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol).type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkEmail\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&\n      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkDomainSizeMax\n    ) {\n      const next = code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/blank-line.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blankLine: () => (/* binding */ blankLine)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst blankLine = {partial: true, tokenize: tokenizeBlankLine}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownLineEnding)(code) ? ok(code) : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/block-quote.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockQuote: () => (/* binding */ blockQuote)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst blockQuote = {\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan) {\n      const state = self.containerState\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      // Always populated by defaults.\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        contBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/character-escape.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEscape: () => (/* binding */ characterEscape)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\\\`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiPunctuation)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/character-reference.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterReference: () => (/* binding */ characterReference)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.ampersand, 'expected `&`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.numberSign) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n    max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceNamedSizeMax\n    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lowercaseX) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n      max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceHexadecimalSizeMax\n      test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiHexDigit\n      return value\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n    max = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceDecimalSizeMax\n    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.semicolon && size) {\n      const token = effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n\n      if (\n        test === micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric &&\n        !(0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__.decodeNamedCharacterReference)(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeFenced: () => (/* binding */ codeFenced)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n}\n\n/** @type {Construct} */\nconst codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {partial: true, tokenize: tokenizeCloseStart}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, infoBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n          effects,\n          beforeContentChunk,\n          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n      return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n            effects,\n            beforeSequenceClose,\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n          ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, sequenceCloseAfter, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-indented.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeIndented: () => (/* binding */ codeIndented)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {partial: true, tokenize: tokenizeFurtherStart}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownSpace)(code))\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      afterPrefix,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      return after(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      afterPrefix,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? ok(code)\n      : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)\n        ? furtherStart(code)\n        : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2NvZGUtaW5kZW50ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFbUM7QUFDaUI7QUFDc0I7QUFDYjs7QUFFN0QsV0FBVyxXQUFXO0FBQ2Y7QUFDUDtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxXQUFXO0FBQ3RCLHNCQUFzQjs7QUFFdEI7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU0sQ0FBQyx1RUFBYTtBQUN4QixrQkFBa0Isd0RBQUs7QUFDdkI7QUFDQTtBQUNBLFdBQVcscUVBQVk7QUFDdkI7QUFDQTtBQUNBLE1BQU0sd0RBQUs7QUFDWCxNQUFNLDREQUFTO0FBQ2Y7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHdEQUFLO0FBQzVCLHNEQUFzRCw0REFBUztBQUMvRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsaUJBQWlCLHdEQUFLO0FBQ3RCO0FBQ0E7O0FBRUEsUUFBUSw0RUFBa0I7QUFDMUI7QUFDQTs7QUFFQSxrQkFBa0Isd0RBQUs7QUFDdkI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxpQkFBaUIsd0RBQUssUUFBUSw0RUFBa0I7QUFDaEQsbUJBQW1CLHdEQUFLO0FBQ3hCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGlCQUFpQix3REFBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxRQUFRLDRFQUFrQjtBQUMxQixvQkFBb0Isd0RBQUs7QUFDekI7QUFDQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHFFQUFZO0FBQ3ZCO0FBQ0E7QUFDQSxNQUFNLHdEQUFLO0FBQ1gsTUFBTSw0REFBUztBQUNmO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix3REFBSztBQUM1QixzREFBc0QsNERBQVM7QUFDL0Q7QUFDQSxRQUFRLDRFQUFrQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmtcXGRldlxcbGliXFxjb2RlLWluZGVudGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7XG4gKiAgIENvbnN0cnVjdCxcbiAqICAgU3RhdGUsXG4gKiAgIFRva2VuaXplQ29udGV4dCxcbiAqICAgVG9rZW5pemVyXG4gKiB9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5pbXBvcnQge2ZhY3RvcnlTcGFjZX0gZnJvbSAnbWljcm9tYXJrLWZhY3Rvcnktc3BhY2UnXG5pbXBvcnQge21hcmtkb3duTGluZUVuZGluZywgbWFya2Rvd25TcGFjZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyJ1xuaW1wb3J0IHtjb2RlcywgY29uc3RhbnRzLCB0eXBlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG4vKiogQHR5cGUge0NvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBjb2RlSW5kZW50ZWQgPSB7XG4gIG5hbWU6ICdjb2RlSW5kZW50ZWQnLFxuICB0b2tlbml6ZTogdG9rZW5pemVDb2RlSW5kZW50ZWRcbn1cblxuLyoqIEB0eXBlIHtDb25zdHJ1Y3R9ICovXG5jb25zdCBmdXJ0aGVyU3RhcnQgPSB7cGFydGlhbDogdHJ1ZSwgdG9rZW5pemU6IHRva2VuaXplRnVydGhlclN0YXJ0fVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiAgIENvbnRleHQuXG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZUNvZGVJbmRlbnRlZChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG4gIHJldHVybiBzdGFydFxuXG4gIC8qKlxuICAgKiBTdGFydCBvZiBjb2RlIChpbmRlbnRlZCkuXG4gICAqXG4gICAqID4gKipQYXJzaW5nIG5vdGUqKjogaXQgaXMgbm90IG5lZWRlZCB0byBjaGVjayBpZiB0aGlzIGZpcnN0IGxpbmUgaXMgYVxuICAgKiA+IGZpbGxlZCBsaW5lICh0aGF0IGl0IGhhcyBhIG5vbi13aGl0ZXNwYWNlIGNoYXJhY3RlciksIGJlY2F1c2UgYmxhbmsgbGluZXNcbiAgICogPiBhcmUgcGFyc2VkIGFscmVhZHksIHNvIHdlIG5ldmVyIHJ1biBpbnRvIHRoYXQuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCAgICAgYWFhXG4gICAqICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgLy8gVG8gZG86IG1hbnVhbGx5IGNoZWNrIGlmIGludGVycnVwdGluZyBsaWtlIGBtYXJrZG93bi1yc2AuXG4gICAgYXNzZXJ0KG1hcmtkb3duU3BhY2UoY29kZSkpXG4gICAgZWZmZWN0cy5lbnRlcih0eXBlcy5jb2RlSW5kZW50ZWQpXG4gICAgLy8gVG8gZG86IHVzZSBhbiBpbXByb3ZlZCBgc3BhY2Vfb3JfdGFiYCBmdW5jdGlvbiBsaWtlIGBtYXJrZG93bi1yc2AsXG4gICAgLy8gc28gdGhhdCB3ZSBjYW4gZHJvcCB0aGUgbmV4dCBzdGF0ZS5cbiAgICByZXR1cm4gZmFjdG9yeVNwYWNlKFxuICAgICAgZWZmZWN0cyxcbiAgICAgIGFmdGVyUHJlZml4LFxuICAgICAgdHlwZXMubGluZVByZWZpeCxcbiAgICAgIGNvbnN0YW50cy50YWJTaXplICsgMVxuICAgICkoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBdCBzdGFydCwgYWZ0ZXIgMSBvciA0IHNwYWNlcy5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8ICAgICBhYWFcbiAgICogICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlclByZWZpeChjb2RlKSB7XG4gICAgY29uc3QgdGFpbCA9IHNlbGYuZXZlbnRzW3NlbGYuZXZlbnRzLmxlbmd0aCAtIDFdXG4gICAgcmV0dXJuIHRhaWwgJiZcbiAgICAgIHRhaWxbMV0udHlwZSA9PT0gdHlwZXMubGluZVByZWZpeCAmJlxuICAgICAgdGFpbFsyXS5zbGljZVNlcmlhbGl6ZSh0YWlsWzFdLCB0cnVlKS5sZW5ndGggPj0gY29uc3RhbnRzLnRhYlNpemVcbiAgICAgID8gYXRCcmVhayhjb2RlKVxuICAgICAgOiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBdCBhIGJyZWFrLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgICAgIGFhYVxuICAgKiAgICAgICAgIF4gIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGF0QnJlYWsoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgIHJldHVybiBhZnRlcihjb2RlKVxuICAgIH1cblxuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIHJldHVybiBlZmZlY3RzLmF0dGVtcHQoZnVydGhlclN0YXJ0LCBhdEJyZWFrLCBhZnRlcikoY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmNvZGVGbG93VmFsdWUpXG4gICAgcmV0dXJuIGluc2lkZShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEluIGNvZGUgY29udGVudC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8ICAgICBhYWFcbiAgICogICAgICAgICBeXl5eXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBpbnNpZGUoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YgfHwgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICBlZmZlY3RzLmV4aXQodHlwZXMuY29kZUZsb3dWYWx1ZSlcbiAgICAgIHJldHVybiBhdEJyZWFrKGNvZGUpXG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIGluc2lkZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gYWZ0ZXIoY29kZSkge1xuICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5jb2RlSW5kZW50ZWQpXG4gICAgLy8gVG8gZG86IGFsbG93IGludGVycnVwdGluZyBsaWtlIGBtYXJrZG93bi1yc2AuXG4gICAgLy8gRmVlbCBmcmVlIHRvIGludGVycnVwdC5cbiAgICAvLyB0b2tlbml6ZXIuaW50ZXJydXB0ID0gZmFsc2VcbiAgICByZXR1cm4gb2soY29kZSlcbiAgfVxufVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiAgIENvbnRleHQuXG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZUZ1cnRoZXJTdGFydChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG5cbiAgcmV0dXJuIGZ1cnRoZXJTdGFydFxuXG4gIC8qKlxuICAgKiBBdCBlb2wsIHRyeWluZyB0byBwYXJzZSBhbm90aGVyIGluZGVudC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8ICAgICBhYWFcbiAgICogICAgICAgICAgICBeXG4gICAqICAgfCAgICAgYmJiXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBmdXJ0aGVyU3RhcnQoY29kZSkge1xuICAgIC8vIFRvIGRvOiBpbXByb3ZlIGBsYXp5YCAvIGBwaWVyY2VgIGhhbmRsaW5nLlxuICAgIC8vIElmIHRoaXMgaXMgYSBsYXp5IGxpbmUsIGl0IGNhbuKAmXQgYmUgY29kZS5cbiAgICBpZiAoc2VsZi5wYXJzZXIubGF6eVtzZWxmLm5vdygpLmxpbmVdKSB7XG4gICAgICByZXR1cm4gbm9rKGNvZGUpXG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5lbnRlcih0eXBlcy5saW5lRW5kaW5nKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBlZmZlY3RzLmV4aXQodHlwZXMubGluZUVuZGluZylcbiAgICAgIHJldHVybiBmdXJ0aGVyU3RhcnRcbiAgICB9XG5cbiAgICAvLyBUbyBkbzogdGhlIGNvZGUgaGVyZSBpbiBgbWljcm9tYXJrLWpzYCBpcyBhIGJpdCBkaWZmZXJlbnQgZnJvbVxuICAgIC8vIGBtYXJrZG93bi1yc2AgYmVjYXVzZSB0aGVyZSBpdCBjYW4gYXR0ZW1wdCBzcGFjZXMuXG4gICAgLy8gV2UgY2Fu4oCZdCB5ZXQuXG4gICAgLy9cbiAgICAvLyBUbyBkbzogdXNlIGFuIGltcHJvdmVkIGBzcGFjZV9vcl90YWJgIGZ1bmN0aW9uIGxpa2UgYG1hcmtkb3duLXJzYCxcbiAgICAvLyBzbyB0aGF0IHdlIGNhbiBkcm9wIHRoZSBuZXh0IHN0YXRlLlxuICAgIHJldHVybiBmYWN0b3J5U3BhY2UoXG4gICAgICBlZmZlY3RzLFxuICAgICAgYWZ0ZXJQcmVmaXgsXG4gICAgICB0eXBlcy5saW5lUHJlZml4LFxuICAgICAgY29uc3RhbnRzLnRhYlNpemUgKyAxXG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEF0IHN0YXJ0LCBhZnRlciAxIG9yIDQgc3BhY2VzLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgICAgIGFhYVxuICAgKiAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGFmdGVyUHJlZml4KGNvZGUpIHtcbiAgICBjb25zdCB0YWlsID0gc2VsZi5ldmVudHNbc2VsZi5ldmVudHMubGVuZ3RoIC0gMV1cbiAgICByZXR1cm4gdGFpbCAmJlxuICAgICAgdGFpbFsxXS50eXBlID09PSB0eXBlcy5saW5lUHJlZml4ICYmXG4gICAgICB0YWlsWzJdLnNsaWNlU2VyaWFsaXplKHRhaWxbMV0sIHRydWUpLmxlbmd0aCA+PSBjb25zdGFudHMudGFiU2l6ZVxuICAgICAgPyBvayhjb2RlKVxuICAgICAgOiBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSlcbiAgICAgICAgPyBmdXJ0aGVyU3RhcnQoY29kZSlcbiAgICAgICAgOiBub2soY29kZSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/code-text.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeText: () => (/* binding */ codeText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextPadding\n        events[tailExitIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding\n    ) {\n      events[enter][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent, 'expected `` ` ``')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeText)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {\n      token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextSequence)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.codeTextData\n    return data(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js":
/*!*******************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/content.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nconst content = {resolve: resolveContent, tokenize: tokenizeContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {partial: true, tokenize: tokenizeContinuation}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  ;(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.content)\n    previous = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(previous, 'expected previous token')\n    previous.next = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected a line ending')\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, prefixed, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-destination */ \"(ssr)/./node_modules/micromark-factory-destination/dev/index.js\");\n/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-label */ \"(ssr)/./node_modules/micromark-factory-label/dev/index.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-factory-title */ \"(ssr)/./node_modules/micromark-factory-title/dev/index.js\");\n/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-whitespace */ \"(ssr)/./node_modules/micromark-factory-whitespace/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {partial: true, tokenize: tokenizeTitleBefore}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')\n    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__.factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabel,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabelMarker,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__.normalizeIdentifier)(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__.factoryDestination)(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestination,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationLiteral,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationLiteralMarker,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationRaw,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(effects, afterWhitespace, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__.factoryTitle)(\n      effects,\n      titleAfter,\n      nok,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitle,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitleMarker,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(\n          effects,\n          titleAfterOptionalWhitespace,\n          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code) ? ok(code) : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2RlZmluaXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRW1DO0FBQzZCO0FBQ1o7QUFDQTtBQUNBO0FBQ1U7QUFLN0I7QUFDc0M7QUFDckI7O0FBRWxELFdBQVcsV0FBVztBQUNmLG9CQUFvQjs7QUFFM0IsV0FBVyxXQUFXO0FBQ3RCLHFCQUFxQjs7QUFFckI7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBSztBQUN2QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwwQ0FBTSxVQUFVLHdEQUFLO0FBQ3pCLFdBQVcsaUVBQVk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sd0RBQUs7QUFDWCxNQUFNLHdEQUFLO0FBQ1gsTUFBTSx3REFBSztBQUNYO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsaUJBQWlCLHdGQUFtQjtBQUNwQztBQUNBOztBQUVBLGlCQUFpQix3REFBSztBQUN0QixvQkFBb0Isd0RBQUs7QUFDekI7QUFDQSxtQkFBbUIsd0RBQUs7QUFDeEI7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtRkFBeUI7QUFDcEMsUUFBUSwrRUFBaUI7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLGlGQUFrQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sd0RBQUs7QUFDWCxNQUFNLHdEQUFLO0FBQ1gsTUFBTSx3REFBSztBQUNYLE1BQU0sd0RBQUs7QUFDWCxNQUFNLHdEQUFLO0FBQ1g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHVFQUFhO0FBQ3hCLFFBQVEscUVBQVksMkJBQTJCLHdEQUFLO0FBQ3BEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGlCQUFpQix3REFBSyxRQUFRLDRFQUFrQjtBQUNoRCxtQkFBbUIsd0RBQUs7O0FBRXhCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsV0FBVyxtRkFBeUI7QUFDcEMsUUFBUSwrRUFBaUI7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFdBQVcscUVBQVk7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsTUFBTSx3REFBSztBQUNYLE1BQU0sd0RBQUs7QUFDWCxNQUFNLHdEQUFLO0FBQ1g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHVFQUFhO0FBQ3hCLFFBQVEscUVBQVk7QUFDcEI7QUFDQTtBQUNBLFVBQVUsd0RBQUs7QUFDZjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0Esb0JBQW9CLHdEQUFLLFFBQVEsNEVBQWtCO0FBQ25EO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxWZWxvY2l0eVxcc3R1ZHlidWRkeS1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtaWNyb21hcmstY29yZS1jb21tb25tYXJrXFxkZXZcXGxpYlxcZGVmaW5pdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1xuICogICBDb25zdHJ1Y3QsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHQsXG4gKiAgIFRva2VuaXplclxuICogfSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHtmYWN0b3J5RGVzdGluYXRpb259IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LWRlc3RpbmF0aW9uJ1xuaW1wb3J0IHtmYWN0b3J5TGFiZWx9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LWxhYmVsJ1xuaW1wb3J0IHtmYWN0b3J5U3BhY2V9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXNwYWNlJ1xuaW1wb3J0IHtmYWN0b3J5VGl0bGV9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXRpdGxlJ1xuaW1wb3J0IHtmYWN0b3J5V2hpdGVzcGFjZX0gZnJvbSAnbWljcm9tYXJrLWZhY3Rvcnktd2hpdGVzcGFjZSdcbmltcG9ydCB7XG4gIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UsXG4gIG1hcmtkb3duTGluZUVuZGluZyxcbiAgbWFya2Rvd25TcGFjZVxufSBmcm9tICdtaWNyb21hcmstdXRpbC1jaGFyYWN0ZXInXG5pbXBvcnQge25vcm1hbGl6ZUlkZW50aWZpZXJ9IGZyb20gJ21pY3JvbWFyay11dGlsLW5vcm1hbGl6ZS1pZGVudGlmaWVyJ1xuaW1wb3J0IHtjb2RlcywgdHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcblxuLyoqIEB0eXBlIHtDb25zdHJ1Y3R9ICovXG5leHBvcnQgY29uc3QgZGVmaW5pdGlvbiA9IHtuYW1lOiAnZGVmaW5pdGlvbicsIHRva2VuaXplOiB0b2tlbml6ZURlZmluaXRpb259XG5cbi8qKiBAdHlwZSB7Q29uc3RydWN0fSAqL1xuY29uc3QgdGl0bGVCZWZvcmUgPSB7cGFydGlhbDogdHJ1ZSwgdG9rZW5pemU6IHRva2VuaXplVGl0bGVCZWZvcmV9XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgQ29udGV4dC5cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplRGVmaW5pdGlvbihlZmZlY3RzLCBvaywgbm9rKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG4gIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICBsZXQgaWRlbnRpZmllclxuXG4gIHJldHVybiBzdGFydFxuXG4gIC8qKlxuICAgKiBBdCBzdGFydCBvZiBhIGRlZmluaXRpb24uXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGIgXCJjXCJcbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAvLyBEbyBub3QgaW50ZXJydXB0IHBhcmFncmFwaHMgKGJ1dCBkbyBmb2xsb3cgZGVmaW5pdGlvbnMpLlxuICAgIC8vIFRvIGRvOiBkbyBgaW50ZXJydXB0YCB0aGUgd2F5IGBtYXJrZG93bi1yc2AgZG9lcy5cbiAgICAvLyBUbyBkbzogcGFyc2Ugd2hpdGVzcGFjZSB0aGUgd2F5IGBtYXJrZG93bi1yc2AgZG9lcy5cbiAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmRlZmluaXRpb24pXG4gICAgcmV0dXJuIGJlZm9yZShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIG9wdGlvbmFsIHdoaXRlc3BhY2UsIGF0IGBbYC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gYmVmb3JlKGNvZGUpIHtcbiAgICAvLyBUbyBkbzogcGFyc2Ugd2hpdGVzcGFjZSB0aGUgd2F5IGBtYXJrZG93bi1yc2AgZG9lcy5cbiAgICBhc3NlcnQoY29kZSA9PT0gY29kZXMubGVmdFNxdWFyZUJyYWNrZXQsICdleHBlY3RlZCBgW2AnKVxuICAgIHJldHVybiBmYWN0b3J5TGFiZWwuY2FsbChcbiAgICAgIHNlbGYsXG4gICAgICBlZmZlY3RzLFxuICAgICAgbGFiZWxBZnRlcixcbiAgICAgIC8vIE5vdGU6IHdlIGRvbuKAmXQgbmVlZCB0byByZXNldCB0aGUgd2F5IGBtYXJrZG93bi1yc2AgZG9lcy5cbiAgICAgIG5vayxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25MYWJlbCxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25MYWJlbE1hcmtlcixcbiAgICAgIHR5cGVzLmRlZmluaXRpb25MYWJlbFN0cmluZ1xuICAgICkoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBsYWJlbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gbGFiZWxBZnRlcihjb2RlKSB7XG4gICAgaWRlbnRpZmllciA9IG5vcm1hbGl6ZUlkZW50aWZpZXIoXG4gICAgICBzZWxmLnNsaWNlU2VyaWFsaXplKHNlbGYuZXZlbnRzW3NlbGYuZXZlbnRzLmxlbmd0aCAtIDFdWzFdKS5zbGljZSgxLCAtMSlcbiAgICApXG5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMuY29sb24pIHtcbiAgICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuZGVmaW5pdGlvbk1hcmtlcilcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgZWZmZWN0cy5leGl0KHR5cGVzLmRlZmluaXRpb25NYXJrZXIpXG4gICAgICByZXR1cm4gbWFya2VyQWZ0ZXJcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgbWFya2VyLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gbWFya2VyQWZ0ZXIoY29kZSkge1xuICAgIC8vIE5vdGU6IHdoaXRlc3BhY2UgaXMgb3B0aW9uYWwuXG4gICAgcmV0dXJuIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSlcbiAgICAgID8gZmFjdG9yeVdoaXRlc3BhY2UoZWZmZWN0cywgZGVzdGluYXRpb25CZWZvcmUpKGNvZGUpXG4gICAgICA6IGRlc3RpbmF0aW9uQmVmb3JlKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQmVmb3JlIGRlc3RpbmF0aW9uLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGRlc3RpbmF0aW9uQmVmb3JlKGNvZGUpIHtcbiAgICByZXR1cm4gZmFjdG9yeURlc3RpbmF0aW9uKFxuICAgICAgZWZmZWN0cyxcbiAgICAgIGRlc3RpbmF0aW9uQWZ0ZXIsXG4gICAgICAvLyBOb3RlOiB3ZSBkb27igJl0IG5lZWQgdG8gcmVzZXQgdGhlIHdheSBgbWFya2Rvd24tcnNgIGRvZXMuXG4gICAgICBub2ssXG4gICAgICB0eXBlcy5kZWZpbml0aW9uRGVzdGluYXRpb24sXG4gICAgICB0eXBlcy5kZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkRlc3RpbmF0aW9uTGl0ZXJhbE1hcmtlcixcbiAgICAgIHR5cGVzLmRlZmluaXRpb25EZXN0aW5hdGlvblJhdyxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25EZXN0aW5hdGlvblN0cmluZ1xuICAgICkoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBkZXN0aW5hdGlvbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gZGVzdGluYXRpb25BZnRlcihjb2RlKSB7XG4gICAgcmV0dXJuIGVmZmVjdHMuYXR0ZW1wdCh0aXRsZUJlZm9yZSwgYWZ0ZXIsIGFmdGVyKShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGRlZmluaXRpb24uXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGJcbiAgICogICAgICAgICAgIF5cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGFmdGVyKGNvZGUpIHtcbiAgICByZXR1cm4gbWFya2Rvd25TcGFjZShjb2RlKVxuICAgICAgPyBmYWN0b3J5U3BhY2UoZWZmZWN0cywgYWZ0ZXJXaGl0ZXNwYWNlLCB0eXBlcy53aGl0ZXNwYWNlKShjb2RlKVxuICAgICAgOiBhZnRlcldoaXRlc3BhY2UoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBkZWZpbml0aW9uLCBhZnRlciBvcHRpb25hbCB3aGl0ZXNwYWNlLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiXG4gICAqICAgICAgICAgICBeXG4gICAqID4gfCBbYV06IGIgXCJjXCJcbiAgICogICAgICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlcldoaXRlc3BhY2UoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YgfHwgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpKSB7XG4gICAgICBlZmZlY3RzLmV4aXQodHlwZXMuZGVmaW5pdGlvbilcblxuICAgICAgLy8gTm90ZTogd2UgZG9u4oCZdCBjYXJlIGFib3V0IHVuaXF1ZW5lc3MuXG4gICAgICAvLyBJdOKAmXMgbGlrZWx5IHRoYXQgdGhhdCBkb2VzbuKAmXQgaGFwcGVuIHZlcnkgZnJlcXVlbnRseS5cbiAgICAgIC8vIEl0IGlzIG1vcmUgbGlrZWx5IHRoYXQgaXQgd2FzdGVzIHByZWNpb3VzIHRpbWUuXG4gICAgICBzZWxmLnBhcnNlci5kZWZpbmVkLnB1c2goaWRlbnRpZmllcilcblxuICAgICAgLy8gVG8gZG86IGBtYXJrZG93bi1yc2AgaW50ZXJydXB0LlxuICAgICAgLy8gLy8gWW914oCZZCBiZSBpbnRlcnJ1cHRpbmcuXG4gICAgICAvLyB0b2tlbml6ZXIuaW50ZXJydXB0ID0gdHJ1ZVxuICAgICAgcmV0dXJuIG9rKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgQ29udGV4dC5cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplVGl0bGVCZWZvcmUoZWZmZWN0cywgb2ssIG5vaykge1xuICByZXR1cm4gdGl0bGVCZWZvcmVcblxuICAvKipcbiAgICogQWZ0ZXIgZGVzdGluYXRpb24sIGF0IHdoaXRlc3BhY2UuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGJcbiAgICogICAgICAgICAgIF5cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gdGl0bGVCZWZvcmUoY29kZSkge1xuICAgIHJldHVybiBtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpXG4gICAgICA/IGZhY3RvcnlXaGl0ZXNwYWNlKGVmZmVjdHMsIGJlZm9yZU1hcmtlcikoY29kZSlcbiAgICAgIDogbm9rKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQXQgdGl0bGUuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqICAgfCBbYV06IGJcbiAgICogPiB8IFwiY1wiXG4gICAqICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBiZWZvcmVNYXJrZXIoY29kZSkge1xuICAgIHJldHVybiBmYWN0b3J5VGl0bGUoXG4gICAgICBlZmZlY3RzLFxuICAgICAgdGl0bGVBZnRlcixcbiAgICAgIG5vayxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25UaXRsZSxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25UaXRsZU1hcmtlcixcbiAgICAgIHR5cGVzLmRlZmluaXRpb25UaXRsZVN0cmluZ1xuICAgICkoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciB0aXRsZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHRpdGxlQWZ0ZXIoY29kZSkge1xuICAgIHJldHVybiBtYXJrZG93blNwYWNlKGNvZGUpXG4gICAgICA/IGZhY3RvcnlTcGFjZShcbiAgICAgICAgICBlZmZlY3RzLFxuICAgICAgICAgIHRpdGxlQWZ0ZXJPcHRpb25hbFdoaXRlc3BhY2UsXG4gICAgICAgICAgdHlwZXMud2hpdGVzcGFjZVxuICAgICAgICApKGNvZGUpXG4gICAgICA6IHRpdGxlQWZ0ZXJPcHRpb25hbFdoaXRlc3BhY2UoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciB0aXRsZSwgYWZ0ZXIgb3B0aW9uYWwgd2hpdGVzcGFjZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHRpdGxlQWZ0ZXJPcHRpb25hbFdoaXRlc3BhY2UoY29kZSkge1xuICAgIHJldHVybiBjb2RlID09PSBjb2Rlcy5lb2YgfHwgbWFya2Rvd25MaW5lRW5kaW5nKGNvZGUpID8gb2soY29kZSkgOiBub2soY29kZSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreakEscape: () => (/* binding */ hardBreakEscape)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\\\`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headingAtx: () => (/* binding */ headingAtx)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.contentTypeText\n    }\n\n    ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign, 'expected `#`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign &&\n      size++ < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, atBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/html-flow.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlFlow: () => (/* binding */ htmlFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-html-tag-name */ \"(ssr)/./node_modules/micromark-util-html-tag-name/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark) {\n      effects.consume(code)\n      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)\n    ) {\n      const slash = code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {\n        marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n          ? completeClosingTagAfter(code)\n          : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code) &&\n      (marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic || marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code) && buffer.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRawSizeMax) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== null) // Always the case.\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected a line ending')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    return effects.attempt(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, ok, nok)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/html-text.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlText: () => (/* binding */ htmlText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan\n      ? end(code)\n      : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash\n        ? commentClose(code)\n        : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return end(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(returnState, 'expected return state')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n          effects,\n          lineEndingAfterPrefix,\n          micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    return returnState(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-end.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelEnd: () => (/* binding */ labelEnd)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-destination */ \"(ssr)/./node_modules/micromark-factory-destination/dev/index.js\");\n/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-factory-label */ \"(ssr)/./node_modules/micromark-factory-label/dev/index.js\");\n/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-factory-title */ \"(ssr)/./node_modules/micromark-factory-title/dev/index.js\");\n/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-whitespace */ \"(ssr)/./node_modules/micromark-factory-whitespace/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n  /** @type {Array<Event>} */\n  const newEvents = []\n  while (++index < events.length) {\n    const token = events[index][1]\n    newEvents.push(events[index])\n\n    if (\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd\n    ) {\n      // Remove the marker.\n      const offset = token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ? 4 : 2\n      token.type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data\n      index += offset\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, 0, events.length, newEvents)\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.link ||\n        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage || token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd) {\n      close = index\n    }\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(open !== undefined, '`open` is supposed to be found')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.link : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.image,\n    start: {...events[open][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  const label = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.label,\n    start: {...events[open][1].start},\n    end: {...events[close][1].end}\n  }\n\n  const text = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelText,\n    start: {...events[open + offset + 2][1].end},\n    end: {...events[close - 2][1].start}\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(\n    media,\n    (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, events.slice(close + 1))\n\n  // Media close.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.push)(media, [['exit', group, context]])\n\n  ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_1__.splice)(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelImage ||\n        self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelMarker)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis, 'expected left paren')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resource)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__.factoryDestination)(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestination,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationLiteral,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationLiteralMarker,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationRaw,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceDestinationString,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis\n    ) {\n      return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__.factoryTitle)(\n        effects,\n        resourceTitleAfter,\n        nok,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitle,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitleMarker,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resourceMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')\n    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__.factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.referenceMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelStartImage: () => (/* binding */ labelStartImage)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst labelStartImage = {\n  name: 'labelStartImage',\n  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_0__.labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartImage\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark, 'expected `!`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelStartLink: () => (/* binding */ labelStartLink)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n/** @type {Construct} */\nconst labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_0__.labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/line-ending.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lineEnding: () => (/* binding */ lineEnding)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, ok, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2xpbmUtZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVtQztBQUNpQjtBQUNPO0FBQ2hCOztBQUUzQyxXQUFXLFdBQVc7QUFDZixvQkFBb0I7O0FBRTNCO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLE9BQU87QUFDcEI7QUFDQSxJQUFJLDJDQUFNLENBQUMsNEVBQWtCO0FBQzdCLGtCQUFrQix3REFBSztBQUN2QjtBQUNBLGlCQUFpQix3REFBSztBQUN0QixXQUFXLHFFQUFZLGNBQWMsd0RBQUs7QUFDMUM7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmtcXGRldlxcbGliXFxsaW5lLWVuZGluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1xuICogICBDb25zdHJ1Y3QsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHQsXG4gKiAgIFRva2VuaXplclxuICogfSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHtmYWN0b3J5U3BhY2V9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXNwYWNlJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7dHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcblxuLyoqIEB0eXBlIHtDb25zdHJ1Y3R9ICovXG5leHBvcnQgY29uc3QgbGluZUVuZGluZyA9IHtuYW1lOiAnbGluZUVuZGluZycsIHRva2VuaXplOiB0b2tlbml6ZUxpbmVFbmRpbmd9XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgQ29udGV4dC5cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplTGluZUVuZGluZyhlZmZlY3RzLCBvaykge1xuICByZXR1cm4gc3RhcnRcblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBzdGFydChjb2RlKSB7XG4gICAgYXNzZXJ0KG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSwgJ2V4cGVjdGVkIGVvbCcpXG4gICAgZWZmZWN0cy5lbnRlcih0eXBlcy5saW5lRW5kaW5nKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5saW5lRW5kaW5nKVxuICAgIHJldHVybiBmYWN0b3J5U3BhY2UoZWZmZWN0cywgb2ssIHR5cGVzLmxpbmVQcmVmaXgpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst list = {\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd,\n  name: 'list',\n  tokenize: tokenizeListStart\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  partial: true,\n  tokenize: tokenizeListItemPrefixWhitespace\n}\n\n/** @type {Construct} */\nconst indentConstruct = {partial: true, tokenize: tokenizeIndent}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.plusSign || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash\n        ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered\n        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listOrdered)\n\n    if (\n      kind === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listUnordered) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix)\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash\n          ? effects.check(_thematic_break_js__WEBPACK_IMPORTED_MODULE_4__.thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.digit1) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix)\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code) && ++size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dot)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      _blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n      effects,\n      ok,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n      effects,\n      effects.attempt(list, ok, nok),\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof self.containerState.size === 'number', 'expected size')\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n    effects,\n    afterPrefix,\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Exiter}\n */\nfunction tokenizeListEnd(effects) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(this.containerState, 'expected state')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n    effects,\n    afterPrefix,\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code) &&\n      tail &&\n      tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js":
/*!****************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setextUnderline: () => (/* binding */ setextUnderline)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(text !== undefined, 'expected a `text` index to be found')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(content !== undefined, 'expected a `text` index to be found')\n\n  const heading = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeading,\n    start: {...events[text][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = {...events[definition][1].end}\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding &&\n        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix &&\n        self.events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.content\n      ) {\n        paragraph = self.events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLineSequence)\n\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.thematicBreakMarkerCountMin &&\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.thematicBreakSequence)\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, atBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\n");

/***/ })

};
;