import { ChatService } from './chat.service';
import { ChatQueryDto } from '../dtos/chatQueryDto';
export declare class ChatController {
    private readonly chatService;
    constructor(chatService: ChatService);
    getEducationalChat(req: any, query: ChatQueryDto): Promise<{
        response: string;
    }>;
    getHistoryForDay(req: any, date: string): Promise<{
        data: (import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>)[];
    } | {
        data: import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>;
    }>;
    getFilteredChatHistory(req: any, upperBound: string, lowerBound: string): Promise<(import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    } & Required<{
        _id: import("mongoose").Types.ObjectId;
    }>)[]>;
    getRecentTopics(req: any): Promise<{
        data: string[];
    }>;
    getTopicChatHistory(req: any, topic: string): Promise<{
        data: {
            subjectWise: {
                queries: import("../schemas/chatHistory.schema").QueryResponse[];
                subject: string;
            }[];
            _id: import("mongoose").Types.ObjectId;
            $locals: Record<string, unknown>;
            $op: "save" | "validate" | "remove" | null;
            $where: Record<string, unknown>;
            baseModelName?: string;
            collection: import("mongoose").Collection;
            db: import("mongoose").Connection;
            errors?: import("mongoose").Error.ValidationError;
            id?: any;
            isNew: boolean;
            schema: import("mongoose").Schema;
            userId: import("mongoose").Types.ObjectId;
            date: String;
            totalTokensSpent: number;
            subjects: String[];
            topics: String[];
            __v: number;
        }[];
    }>;
    getChatStreak(req: any): Promise<{
        streak: number;
    }>;
}
