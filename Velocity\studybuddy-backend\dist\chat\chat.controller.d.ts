import { ChatService } from './chat.service';
import { ChatQueryDto } from '../dtos/chatQueryDto';
export declare class ChatController {
    private readonly chatService;
    constructor(chatService: ChatService);
    getEducationalChat(req: any, query: ChatQueryDto): Promise<{
        response: string;
    }>;
    getHistoryForDay(req: any, date: string): Promise<{
        data: (import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>)[];
    } | {
        data: import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        } & Required<{
            _id: import("mongoose").Types.ObjectId;
        }>;
    }>;
    getFilteredChatHistory(req: any, upperBound: string, lowerBound: string): Promise<(import("mongoose").Document<unknown, {}, import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }> & import("mongoose").Document<unknown, {}, import("../schemas/chatHistory.schema").ChatHistory> & import("../schemas/chatHistory.schema").ChatHistory & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    } & Required<{
        _id: import("mongoose").Types.ObjectId;
    }>)[]>;
    getChatStreak(req: any): Promise<{
        streak: number;
    }>;
}
