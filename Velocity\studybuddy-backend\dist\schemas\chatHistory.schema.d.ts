import { HydratedDocument, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type ChatHistoryDocument = HydratedDocument<ChatHistory>;
export declare class QueryResponse {
    query: string;
    response: string;
    tokensUsed: number;
    summary: string;
    topic: string;
}
export declare class SubjectWise {
    subject: string;
    queries: QueryResponse[];
}
export declare class ChatHistory {
    userId: Types.ObjectId;
    date: String;
    subjectWise: SubjectWise[];
    totalTokensSpent: number;
    subjects: String[];
    topics: String[];
}
declare const QueryResponseSchema: mongoose.Schema<QueryResponse, mongoose.Model<QueryResponse, any, any, any, mongoose.Document<unknown, any, QueryResponse> & QueryResponse & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, QueryResponse, mongoose.Document<unknown, {}, mongoose.FlatRecord<QueryResponse>> & mongoose.FlatRecord<QueryResponse> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
declare const ChatHistorySchema: mongoose.Schema<ChatHistory, mongoose.Model<ChatHistory, any, any, any, mongoose.Document<unknown, any, ChatHistory> & ChatHistory & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, ChatHistory, mongoose.Document<unknown, {}, mongoose.FlatRecord<ChatHistory>> & mongoose.FlatRecord<ChatHistory> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export { ChatHistorySchema, QueryResponseSchema };
