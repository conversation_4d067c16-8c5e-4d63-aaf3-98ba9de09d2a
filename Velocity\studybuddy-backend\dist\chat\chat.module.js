"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const chat_service_1 = require("./chat.service");
const chat_controller_1 = require("./chat.controller");
const users_module_1 = require("../users/users.module");
const mongoose_1 = require("@nestjs/mongoose");
const chatHistory_schema_1 = require("../schemas/chatHistory.schema");
const schedule_1 = require("@nestjs/schedule");
const chat_cleanup_service_1 = require("./chat-cleanup.service");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [
            users_module_1.UsersModule,
            mongoose_1.MongooseModule.forFeature([{ name: chatHistory_schema_1.ChatHistory.name, schema: chatHistory_schema_1.ChatHistorySchema }]),
            schedule_1.ScheduleModule.forRoot()
        ],
        providers: [chat_service_1.ChatService, chat_cleanup_service_1.ChatCleanupService],
        controllers: [chat_controller_1.ChatController]
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map