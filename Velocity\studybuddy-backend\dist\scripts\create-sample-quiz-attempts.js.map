{"version": 3, "file": "create-sample-quiz-attempts.js", "sourceRoot": "", "sources": ["../../src/scripts/create-sample-quiz-attempts.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,8CAA0C;AAE1C,+CAAiD;AACjD,wEAA6D;AAC7D,wDAA8C;AAC9C,8DAAoD;AAEpD,KAAK,UAAU,wBAAwB;IACrC,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;IAElE,MAAM,gBAAgB,GAAG,GAAG,CAAC,GAAG,CAAqB,IAAA,wBAAa,EAAC,iCAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACtF,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAc,IAAA,wBAAa,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,MAAM,YAAY,GAAG,GAAG,CAAC,GAAG,CAAiB,IAAA,wBAAa,EAAC,wBAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1E,IAAI,CAAC;QAEH,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACxE,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;QAElD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,MAAM,cAAc,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;QAG3E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAG9D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAEtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrC,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;gBAG5E,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/D,OAAO,CAAC,GAAG,CAAC,cAAc,aAAa,CAAC,IAAI,wBAAwB,CAAC,CAAC;oBACtE,SAAS;gBACX,CAAC;gBAED,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;gBAGlG,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC;gBAC3G,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC;gBAClE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBAG7D,MAAM,OAAO,GAAG,EAAE,CAAC;gBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxC,OAAO,CAAC,IAAI,CAAC;wBACX,MAAM,EAAE,aAAa,CAAC,GAAG;wBACzB,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC7C,SAAS,EAAE,CAAC,GAAG,cAAc;wBAC7B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;qBAC/C,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,WAAW,GAAG,IAAI,gBAAgB,CAAC;oBACvC,MAAM,EAAE,IAAI,CAAC,GAAG;oBAChB,SAAS,EAAE,aAAa,CAAC,GAAG;oBAC5B,OAAO,EAAE,WAAW,CAAC,GAAG;oBACxB,OAAO,EAAE,OAAO;oBAChB,cAAc,EAAE,cAAc;oBAC9B,cAAc,EAAE,cAAc;oBAC9B,KAAK,EAAE,KAAK;oBACZ,cAAc,EAAE,cAAc;oBAC9B,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;gBAEH,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,2BAA2B,aAAa,CAAC,IAAI,MAAM,KAAK,MAAM,cAAc,IAAI,cAAc,GAAG,CAAC,CAAC;YACjH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;YAAS,CAAC;QACT,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;AACH,CAAC;AAGD,wBAAwB,EAAE,CAAC"}