import { AuthService } from './auth.service';
import { RegisterDto } from 'src/dtos/register.dto';
import { LoginDto } from 'src/dtos/login.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        accessToken: string;
        isUserDetailsPresent: boolean;
    }>;
    register(registerDto: RegisterDto): Promise<{
        success: boolean;
    }>;
}
