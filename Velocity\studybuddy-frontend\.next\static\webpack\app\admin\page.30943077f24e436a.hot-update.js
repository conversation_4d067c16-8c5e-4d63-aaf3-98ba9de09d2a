"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ../use-intersection */ \"(app-pages-browser)/./node_modules/next/dist/client/use-intersection.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction prefetch(router, href, options) {\n    if (typeof window === 'undefined') {\n        return;\n    }\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return router.prefetch(href, options);\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e)) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if ('beforePopState' in router) {\n            router[replace ? 'replace' : 'push'](href, as, {\n                shallow,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? 'replace' : 'push'](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + (typeof window !== 'undefined' ? \"\\nOpen your browser's console to view the Component stack trace.\" : ''));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"Link.LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"Link.LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + (typeof window !== 'undefined' ? \" \\nOpen your browser's console to view the Component stack trace.\" : ''));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor');\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: '200px'\n    });\n    const setIntersectionWithResetRef = _react.default.useCallback({\n        \"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\": (el)=>{\n            // Before the link getting observed, check if visible state need to be reset\n            if (previousAs.current !== as || previousHref.current !== href) {\n                resetVisible();\n                previousAs.current = as;\n                previousHref.current = href;\n            }\n            setIntersectionRef(el);\n        }\n    }[\"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\"], [\n        as,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    const setRef = (0, _usemergedref.useMergedRef)(setIntersectionWithResetRef, childRef);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect({\n        \"Link.LinkComponent.useEffect\": ()=>{\n            // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n            if (true) {\n                return;\n            }\n            if (!router) {\n                return;\n            }\n            // If we don't need to prefetch the URL, don't do prefetch.\n            if (!isVisible || !prefetchEnabled) {\n                return;\n            }\n            // Prefetch the URL.\n            prefetch(router, href, {\n                kind: appPrefetchKind\n            });\n        }\n    }[\"Link.LinkComponent.useEffect\"], [\n        as,\n        href,\n        isVisible,\n        prefetchEnabled,\n        router,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            prefetch(router, href, {\n                kind: appPrefetchKind\n            });\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            prefetch(router, href, {\n                kind: appPrefetchKind\n            });\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\")), \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvZm9ybWF0LXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBQSx1Q0FBdUM7QUFDdkMsc0RBQXNEO0FBQ3RELEVBQUU7QUFDRiwwRUFBMEU7QUFDMUUsZ0VBQWdFO0FBQ2hFLHNFQUFzRTtBQUN0RSxzRUFBc0U7QUFDdEUsNEVBQTRFO0FBQzVFLHFFQUFxRTtBQUNyRSx3QkFBd0I7QUFDeEIsRUFBRTtBQUNGLDBFQUEwRTtBQUMxRSx5REFBeUQ7QUFDekQsRUFBRTtBQUNGLDBFQUEwRTtBQUMxRSw2REFBNkQ7QUFDN0QsNEVBQTRFO0FBQzVFLDJFQUEyRTtBQUMzRSx3RUFBd0U7QUFDeEUsNEVBQTRFO0FBQzVFLHlDQUF5Qzs7Ozs7Ozs7Ozs7OztJQVF6QkEsU0FBUztlQUFUQTs7SUE2REFDLG9CQUFvQjtlQUFwQkE7O0lBZkhDLGFBQWE7ZUFBYkE7Ozs7bUZBbERnQjtBQUU3QixNQUFNQyxtQkFBbUI7QUFFbEIsU0FBU0gsVUFBVUksTUFBaUI7SUFDekMsSUFBSSxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHRjtJQUN6QixJQUFJRyxXQUFXSCxPQUFPRyxRQUFRLElBQUk7SUFDbEMsSUFBSUMsV0FBV0osT0FBT0ksUUFBUSxJQUFJO0lBQ2xDLElBQUlDLE9BQU9MLE9BQU9LLElBQUksSUFBSTtJQUMxQixJQUFJQyxRQUFRTixPQUFPTSxLQUFLLElBQUk7SUFDNUIsSUFBSUMsT0FBdUI7SUFFM0JOLE9BQU9BLE9BQU9PLG1CQUFtQlAsTUFBTVEsT0FBTyxDQUFDLFFBQVEsT0FBTyxNQUFNO0lBRXBFLElBQUlULE9BQU9PLElBQUksRUFBRTtRQUNmQSxPQUFPTixPQUFPRCxPQUFPTyxJQUFJO0lBQzNCLE9BQU8sSUFBSUwsVUFBVTtRQUNuQkssT0FBT04sT0FBUSxFQUFDQyxTQUFTUSxPQUFPLENBQUMsT0FBUSxNQUFHUixXQUFTLE1BQUtBLFFBQUFBLENBQU87UUFDakUsSUFBSUYsT0FBT1csSUFBSSxFQUFFO1lBQ2ZKLFFBQVEsTUFBTVAsT0FBT1csSUFBSTtRQUMzQjtJQUNGO0lBRUEsSUFBSUwsU0FBUyxPQUFPQSxVQUFVLFVBQVU7UUFDdENBLFFBQVFNLE9BQU9DLGFBQVlDLHNCQUFzQixDQUFDUjtJQUNwRDtJQUVBLElBQUlTLFNBQVNmLE9BQU9lLE1BQU0sSUFBS1QsU0FBVSxNQUFHQSxTQUFZO0lBRXhELElBQUlILFlBQVksQ0FBQ0EsU0FBU2EsUUFBUSxDQUFDLE1BQU1iLFlBQVk7SUFFckQsSUFDRUgsT0FBT2lCLE9BQU8sSUFDWixFQUFDZCxZQUFZSixpQkFBaUJtQixJQUFJLENBQUNmLFNBQUFBLENBQVEsSUFBTUksU0FBUyxPQUM1RDtRQUNBQSxPQUFPLE9BQVFBLENBQUFBLFFBQVEsR0FBQztRQUN4QixJQUFJSCxZQUFZQSxRQUFRLENBQUMsRUFBRSxLQUFLLEtBQUtBLFdBQVcsTUFBTUE7SUFDeEQsT0FBTyxJQUFJLENBQUNHLE1BQU07UUFDaEJBLE9BQU87SUFDVDtJQUVBLElBQUlGLFFBQVFBLElBQUksQ0FBQyxFQUFFLEtBQUssS0FBS0EsT0FBTyxNQUFNQTtJQUMxQyxJQUFJVSxVQUFVQSxNQUFNLENBQUMsRUFBRSxLQUFLLEtBQUtBLFNBQVMsTUFBTUE7SUFFaERYLFdBQVdBLFNBQVNLLE9BQU8sQ0FBQyxTQUFTRDtJQUNyQ08sU0FBU0EsT0FBT04sT0FBTyxDQUFDLEtBQUs7SUFFN0IsT0FBUSxLQUFFTixXQUFXSSxPQUFPSCxXQUFXVyxTQUFTVjtBQUNsRDtBQUVPLE1BQU1QLGdCQUFnQjtJQUMzQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVNLFNBQVNELHFCQUFxQnNCLEdBQWM7SUFDakQsSUFBSUMsSUFBb0IsRUFBb0I7UUFDMUMsSUFBSUQsUUFBUSxRQUFRLE9BQU9BLFFBQVEsVUFBVTtZQUMzQ0ksT0FBT0MsSUFBSSxDQUFDTCxLQUFLTSxPQUFPLENBQUMsQ0FBQ0M7Z0JBQ3hCLElBQUksQ0FBQzVCLGNBQWM2QixRQUFRLENBQUNELE1BQU07b0JBQ2hDRSxRQUFRQyxJQUFJLENBQ1QsdURBQW9ESDtnQkFFekQ7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxPQUFPOUIsVUFBVXVCO0FBQ25CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcc2hhcmVkXFxsaWJcXHJvdXRlclxcdXRpbHNcXGZvcm1hdC11cmwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRm9ybWF0IGZ1bmN0aW9uIG1vZGlmaWVkIGZyb20gbm9kZWpzXG4vLyBDb3B5cmlnaHQgSm95ZW50LCBJbmMuIGFuZCBvdGhlciBOb2RlIGNvbnRyaWJ1dG9ycy5cbi8vXG4vLyBQZXJtaXNzaW9uIGlzIGhlcmVieSBncmFudGVkLCBmcmVlIG9mIGNoYXJnZSwgdG8gYW55IHBlcnNvbiBvYnRhaW5pbmcgYVxuLy8gY29weSBvZiB0aGlzIHNvZnR3YXJlIGFuZCBhc3NvY2lhdGVkIGRvY3VtZW50YXRpb24gZmlsZXMgKHRoZVxuLy8gXCJTb2Z0d2FyZVwiKSwgdG8gZGVhbCBpbiB0aGUgU29mdHdhcmUgd2l0aG91dCByZXN0cmljdGlvbiwgaW5jbHVkaW5nXG4vLyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cyB0byB1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsXG4vLyBkaXN0cmlidXRlLCBzdWJsaWNlbnNlLCBhbmQvb3Igc2VsbCBjb3BpZXMgb2YgdGhlIFNvZnR3YXJlLCBhbmQgdG8gcGVybWl0XG4vLyBwZXJzb25zIHRvIHdob20gdGhlIFNvZnR3YXJlIGlzIGZ1cm5pc2hlZCB0byBkbyBzbywgc3ViamVjdCB0byB0aGVcbi8vIGZvbGxvd2luZyBjb25kaXRpb25zOlxuLy9cbi8vIFRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlIGFuZCB0aGlzIHBlcm1pc3Npb24gbm90aWNlIHNoYWxsIGJlIGluY2x1ZGVkXG4vLyBpbiBhbGwgY29waWVzIG9yIHN1YnN0YW50aWFsIHBvcnRpb25zIG9mIHRoZSBTb2Z0d2FyZS5cbi8vXG4vLyBUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiLCBXSVRIT1VUIFdBUlJBTlRZIE9GIEFOWSBLSU5ELCBFWFBSRVNTXG4vLyBPUiBJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GXG4vLyBNRVJDSEFOVEFCSUxJVFksIEZJVE5FU1MgRk9SIEEgUEFSVElDVUxBUiBQVVJQT1NFIEFORCBOT05JTkZSSU5HRU1FTlQuIElOXG4vLyBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SUyBPUiBDT1BZUklHSFQgSE9MREVSUyBCRSBMSUFCTEUgRk9SIEFOWSBDTEFJTSxcbi8vIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUlxuLy8gT1RIRVJXSVNFLCBBUklTSU5HIEZST00sIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFNPRlRXQVJFIE9SIFRIRVxuLy8gVVNFIE9SIE9USEVSIERFQUxJTkdTIElOIFRIRSBTT0ZUV0FSRS5cblxuaW1wb3J0IHR5cGUgeyBVcmxPYmplY3QgfSBmcm9tICd1cmwnXG5pbXBvcnQgdHlwZSB7IFBhcnNlZFVybFF1ZXJ5IH0gZnJvbSAncXVlcnlzdHJpbmcnXG5pbXBvcnQgKiBhcyBxdWVyeXN0cmluZyBmcm9tICcuL3F1ZXJ5c3RyaW5nJ1xuXG5jb25zdCBzbGFzaGVkUHJvdG9jb2xzID0gL2h0dHBzP3xmdHB8Z29waGVyfGZpbGUvXG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRVcmwodXJsT2JqOiBVcmxPYmplY3QpIHtcbiAgbGV0IHsgYXV0aCwgaG9zdG5hbWUgfSA9IHVybE9ialxuICBsZXQgcHJvdG9jb2wgPSB1cmxPYmoucHJvdG9jb2wgfHwgJydcbiAgbGV0IHBhdGhuYW1lID0gdXJsT2JqLnBhdGhuYW1lIHx8ICcnXG4gIGxldCBoYXNoID0gdXJsT2JqLmhhc2ggfHwgJydcbiAgbGV0IHF1ZXJ5ID0gdXJsT2JqLnF1ZXJ5IHx8ICcnXG4gIGxldCBob3N0OiBzdHJpbmcgfCBmYWxzZSA9IGZhbHNlXG5cbiAgYXV0aCA9IGF1dGggPyBlbmNvZGVVUklDb21wb25lbnQoYXV0aCkucmVwbGFjZSgvJTNBL2ksICc6JykgKyAnQCcgOiAnJ1xuXG4gIGlmICh1cmxPYmouaG9zdCkge1xuICAgIGhvc3QgPSBhdXRoICsgdXJsT2JqLmhvc3RcbiAgfSBlbHNlIGlmIChob3N0bmFtZSkge1xuICAgIGhvc3QgPSBhdXRoICsgKH5ob3N0bmFtZS5pbmRleE9mKCc6JykgPyBgWyR7aG9zdG5hbWV9XWAgOiBob3N0bmFtZSlcbiAgICBpZiAodXJsT2JqLnBvcnQpIHtcbiAgICAgIGhvc3QgKz0gJzonICsgdXJsT2JqLnBvcnRcbiAgICB9XG4gIH1cblxuICBpZiAocXVlcnkgJiYgdHlwZW9mIHF1ZXJ5ID09PSAnb2JqZWN0Jykge1xuICAgIHF1ZXJ5ID0gU3RyaW5nKHF1ZXJ5c3RyaW5nLnVybFF1ZXJ5VG9TZWFyY2hQYXJhbXMocXVlcnkgYXMgUGFyc2VkVXJsUXVlcnkpKVxuICB9XG5cbiAgbGV0IHNlYXJjaCA9IHVybE9iai5zZWFyY2ggfHwgKHF1ZXJ5ICYmIGA/JHtxdWVyeX1gKSB8fCAnJ1xuXG4gIGlmIChwcm90b2NvbCAmJiAhcHJvdG9jb2wuZW5kc1dpdGgoJzonKSkgcHJvdG9jb2wgKz0gJzonXG5cbiAgaWYgKFxuICAgIHVybE9iai5zbGFzaGVzIHx8XG4gICAgKCghcHJvdG9jb2wgfHwgc2xhc2hlZFByb3RvY29scy50ZXN0KHByb3RvY29sKSkgJiYgaG9zdCAhPT0gZmFsc2UpXG4gICkge1xuICAgIGhvc3QgPSAnLy8nICsgKGhvc3QgfHwgJycpXG4gICAgaWYgKHBhdGhuYW1lICYmIHBhdGhuYW1lWzBdICE9PSAnLycpIHBhdGhuYW1lID0gJy8nICsgcGF0aG5hbWVcbiAgfSBlbHNlIGlmICghaG9zdCkge1xuICAgIGhvc3QgPSAnJ1xuICB9XG5cbiAgaWYgKGhhc2ggJiYgaGFzaFswXSAhPT0gJyMnKSBoYXNoID0gJyMnICsgaGFzaFxuICBpZiAoc2VhcmNoICYmIHNlYXJjaFswXSAhPT0gJz8nKSBzZWFyY2ggPSAnPycgKyBzZWFyY2hcblxuICBwYXRobmFtZSA9IHBhdGhuYW1lLnJlcGxhY2UoL1s/I10vZywgZW5jb2RlVVJJQ29tcG9uZW50KVxuICBzZWFyY2ggPSBzZWFyY2gucmVwbGFjZSgnIycsICclMjMnKVxuXG4gIHJldHVybiBgJHtwcm90b2NvbH0ke2hvc3R9JHtwYXRobmFtZX0ke3NlYXJjaH0ke2hhc2h9YFxufVxuXG5leHBvcnQgY29uc3QgdXJsT2JqZWN0S2V5cyA9IFtcbiAgJ2F1dGgnLFxuICAnaGFzaCcsXG4gICdob3N0JyxcbiAgJ2hvc3RuYW1lJyxcbiAgJ2hyZWYnLFxuICAncGF0aCcsXG4gICdwYXRobmFtZScsXG4gICdwb3J0JyxcbiAgJ3Byb3RvY29sJyxcbiAgJ3F1ZXJ5JyxcbiAgJ3NlYXJjaCcsXG4gICdzbGFzaGVzJyxcbl1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFdpdGhWYWxpZGF0aW9uKHVybDogVXJsT2JqZWN0KTogc3RyaW5nIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgaWYgKHVybCAhPT0gbnVsbCAmJiB0eXBlb2YgdXJsID09PSAnb2JqZWN0Jykge1xuICAgICAgT2JqZWN0LmtleXModXJsKS5mb3JFYWNoKChrZXkpID0+IHtcbiAgICAgICAgaWYgKCF1cmxPYmplY3RLZXlzLmluY2x1ZGVzKGtleSkpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgICBgVW5rbm93biBrZXkgcGFzc2VkIHZpYSB1cmxPYmplY3QgaW50byB1cmwuZm9ybWF0OiAke2tleX1gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmb3JtYXRVcmwodXJsKVxufVxuIl0sIm5hbWVzIjpbImZvcm1hdFVybCIsImZvcm1hdFdpdGhWYWxpZGF0aW9uIiwidXJsT2JqZWN0S2V5cyIsInNsYXNoZWRQcm90b2NvbHMiLCJ1cmxPYmoiLCJhdXRoIiwiaG9zdG5hbWUiLCJwcm90b2NvbCIsInBhdGhuYW1lIiwiaGFzaCIsInF1ZXJ5IiwiaG9zdCIsImVuY29kZVVSSUNvbXBvbmVudCIsInJlcGxhY2UiLCJpbmRleE9mIiwicG9ydCIsIlN0cmluZyIsInF1ZXJ5c3RyaW5nIiwidXJsUXVlcnlUb1NlYXJjaFBhcmFtcyIsInNlYXJjaCIsImVuZHNXaXRoIiwic2xhc2hlcyIsInRlc3QiLCJ1cmwiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsImluY2x1ZGVzIiwiY29uc29sZSIsIndhcm4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw new Error(message);\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw new Error(message);\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/sidebar */ \"(app-pages-browser)/./src/components/admin/sidebar.tsx\");\n/* harmony import */ var _components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/top-nav */ \"(app-pages-browser)/./src/components/admin/top-nav.tsx\");\n/* harmony import */ var _components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/data-table */ \"(app-pages-browser)/./src/components/admin/data-table.tsx\");\n/* harmony import */ var _components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/add-user-modal */ \"(app-pages-browser)/./src/components/admin/add-user-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-copy.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardCopy,Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/student-details-modal */ \"(app-pages-browser)/./src/components/admin/student-details-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingUsers, setIsLoadingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [decryptionCache, setDecryptionCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [selectedUserForDetails, setSelectedUserForDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const columns = [\n        {\n            key: \"email\",\n            label: \"Mail\"\n        },\n        {\n            key: \"decryptedPassword\",\n            label: \"Password\"\n        }\n    ];\n    const copyUserDetails = (email, password)=>{\n        navigator.clipboard.writeText(\"Email: \".concat(email, \"\\nPassword: \").concat(password));\n        toast({\n            title: \"Copied!\",\n            description: \"User details copied to clipboard\"\n        });\n    };\n    const deleteUser = async (userId)=>{\n        try {\n            const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL, \"/users?id=\").concat(userId), {\n                method: 'DELETE',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) throw new Error('Failed to delete user');\n            setUsers(users.filter((user)=>user._id !== userId));\n            toast({\n                title: 'Success',\n                description: 'User deleted successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to delete user:', err);\n            toast({\n                title: 'Error',\n                description: 'Could not delete user'\n            });\n        }\n    };\n    const actions = [\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'View Details',\n            onClick: (row)=>{\n                setSelectedUserForDetails(row);\n                setIsUserDetailsModalOpen(true);\n            },\n            variant: 'view'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: 'Copy',\n            onClick: (row)=>copyUserDetails(row.email, row.decryptedPassword || row.password),\n            variant: 'edit'\n        },\n        {\n            icon: _barrel_optimize_names_ClipboardCopy_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: 'Delete',\n            onClick: (row)=>deleteUser(row._id),\n            variant: 'delete'\n        }\n    ];\n    const refreshUsers = ()=>{\n        // Force a page reload to refresh data\n        window.location.reload();\n    };\n    const handleAddUser = async (param)=>{\n        let { email, password } = param;\n        try {\n            const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL, \"/auth/register\"), {\n                method: 'POST',\n                headers: getAuthHeaders(),\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) throw new Error('Registration failed');\n            const newUser = await response.json();\n            const decryptedPassword = await decryptPassword(password);\n            setUsers([\n                ...users,\n                {\n                    _id: newUser._id,\n                    email,\n                    password,\n                    decryptedPassword\n                }\n            ]);\n            toast({\n                title: 'Success',\n                description: 'User registered successfully'\n            });\n            // Refresh the user list to ensure consistency\n            setTimeout(()=>refreshUsers(), 1000);\n        } catch (err) {\n            console.error('Failed to register user:', err);\n            toast({\n                title: 'Error',\n                description: 'Failed to register user'\n            });\n        }\n    };\n    // Token retrieval and user fetching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            const loadData = {\n                \"AdminDashboard.useEffect.loadData\": async ()=>{\n                    if (true) {\n                        const stored = localStorage.getItem('accessToken');\n                        if (!stored) {\n                            router.push('/admin-login');\n                            return;\n                        }\n                        setToken(stored);\n                        setIsLoadingUsers(true);\n                        try {\n                            const headers = {\n                                Authorization: \"Bearer \".concat(stored),\n                                'Content-Type': 'application/json'\n                            };\n                            const res = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL, \"/users\"), {\n                                headers,\n                                cache: 'no-cache'\n                            });\n                            if (!res.ok) {\n                                throw new Error(\"fetch failed: \".concat(res.status));\n                            }\n                            const data = await res.json();\n                            console.log(\"Fetched users data:\", data);\n                            const list = await Promise.all(data.map({\n                                \"AdminDashboard.useEffect.loadData\": async (u)=>({\n                                        ...u,\n                                        decryptedPassword: await decryptPassword(u.password)\n                                    })\n                            }[\"AdminDashboard.useEffect.loadData\"]));\n                            setUsers(list);\n                            console.log(\"Users state set to:\", list.length, \"users\");\n                        } catch (error) {\n                            console.error(\"Error loading users:\", error);\n                            toast({\n                                title: 'Error',\n                                description: 'Failed to load users'\n                            });\n                        } finally{\n                            setIsLoadingUsers(false);\n                        }\n                    }\n                }\n            }[\"AdminDashboard.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminDashboard.useCallback[getAuthHeaders]\": ()=>({\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            })\n    }[\"AdminDashboard.useCallback[getAuthHeaders]\"], [\n        token\n    ]);\n    const decryptPassword = async (encrypted)=>{\n        // Return early if no encrypted password or empty string\n        if (!encrypted || encrypted.trim() === '') {\n            return encrypted;\n        }\n        // Check cache first\n        if (decryptionCache.has(encrypted)) {\n            return decryptionCache.get(encrypted);\n        }\n        try {\n            const res = await fetch('/api/decrypt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    encryptedPassword: encrypted\n                })\n            });\n            if (!res.ok) throw new Error('decrypt');\n            const data = await res.json();\n            // Cache the result\n            setDecryptionCache((prev)=>new Map(prev).set(encrypted, data.decryptedPassword));\n            return data.decryptedPassword;\n        } catch (error) {\n            console.error('Decryption failed for:', encrypted, error);\n            return encrypted;\n        }\n    };\n    // Debug log for users state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            console.log(\"Users state changed:\", users.length, \"users\") // Debug log\n            ;\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        users\n    ]);\n    // Debug log for loading state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            console.log(\"Loading state changed:\", isLoadingUsers) // Debug log\n            ;\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        isLoadingUsers\n    ]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    const breadcrumbs = [\n        {\n            label: \"Admin Dashboard\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"Student Details\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_top_nav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: \"Admin Dashboard\",\n                        breadcrumbs: breadcrumbs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-2 bg-yellow-100 text-xs\",\n                                children: [\n                                    \"Debug: users.length=\",\n                                    users.length,\n                                    \", isLoading=\",\n                                    isLoadingUsers,\n                                    \", token=\",\n                                    token ? 'present' : 'missing',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"API URL: \",\n                                    process.env.NEXT_PUBLIC_API_URL\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_data_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                title: \"Students\",\n                                count: users.length,\n                                columns: columns,\n                                data: users,\n                                actions: actions,\n                                onAddNew: ()=>setIsModalOpen(true),\n                                addButtonLabel: \"Add User\",\n                                page: page,\n                                pageSize: pageSize,\n                                onPageChange: setPage,\n                                isLoading: isLoadingUsers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_user_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                onSubmit: handleAddUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_student_details_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isUserDetailsModalOpen,\n                onClose: ()=>setIsUserDetailsModalOpen(false),\n                student: selectedUserForDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_9__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"PAnj+IJckq76NaqXfAAFp7o19AQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdFO0FBQ3JCO0FBQ0s7QUFDRDtBQUNNO0FBQ087QUFDSDtBQUNpQjtBQUM5QjtBQUNLO0FBU2xDLFNBQVNjOztJQUN0QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lCLE9BQU9DLFNBQVMsR0FBR2xCLCtDQUFRQSxDQUFTLEVBQUU7SUFDN0MsTUFBTSxDQUFDbUIsT0FBT0MsU0FBUyxHQUFHcEIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3FCLGdCQUFnQkMsa0JBQWtCLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN1QixpQkFBaUJDLG1CQUFtQixHQUFHeEIsK0NBQVFBLENBQXNCLElBQUl5QjtJQUVoRixNQUFNQyxTQUFTdkIsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRXdCLEtBQUssRUFBRSxHQUFHZiwwREFBUUE7SUFFMUIsTUFBTSxDQUFDZ0Isd0JBQXdCQywwQkFBMEIsR0FBRzdCLCtDQUFRQSxDQUFjO0lBQ2xGLE1BQU0sQ0FBQzhCLHdCQUF3QkMsMEJBQTBCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUVyRSxNQUFNZ0MsVUFBVTtRQUNkO1lBQUVDLEtBQUs7WUFBU0MsT0FBTztRQUFPO1FBQzlCO1lBQUVELEtBQUs7WUFBcUJDLE9BQU87UUFBVztLQUMvQztJQUVELE1BQU1DLGtCQUFrQixDQUFDQyxPQUFlQztRQUN0Q0MsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUMsVUFBOEJILE9BQXBCRCxPQUFNLGdCQUF1QixPQUFUQztRQUM1RFYsTUFBTTtZQUFFYyxPQUFPO1lBQVdDLGFBQWE7UUFBbUM7SUFDNUU7SUFFQSxNQUFNQyxhQUFhLE9BQU9DO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBK0NGLE9BQTVDRyxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQixFQUFDLGNBQW1CLE9BQVBMLFNBQVU7Z0JBQ3BGTSxRQUFRO2dCQUNSQyxTQUFTQztZQUNYO1lBQ0EsSUFBSSxDQUFDUCxTQUFTUSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDcEMsU0FBU0QsTUFBTXNDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsR0FBRyxLQUFLYjtZQUMzQ2pCLE1BQU07Z0JBQUVjLE9BQU87Z0JBQVdDLGFBQWE7WUFBNEI7WUFDbkUsOENBQThDO1lBQzlDZ0IsV0FBVyxJQUFNQyxnQkFBZ0I7UUFDbkMsRUFBRSxPQUFPQyxLQUFLO1lBQ1pDLFFBQVFDLEtBQUssQ0FBQywwQkFBMEJGO1lBQ3hDakMsTUFBTTtnQkFBRWMsT0FBTztnQkFBU0MsYUFBYTtZQUF3QjtRQUMvRDtJQUNGO0lBRUEsTUFBTXFCLFVBQVU7UUFDZDtZQUNFQyxNQUFNeEQscUdBQUdBO1lBQ1QwQixPQUFPO1lBQ1ArQixTQUFTLENBQUNDO2dCQUNSckMsMEJBQTBCcUM7Z0JBQzFCbkMsMEJBQTBCO1lBQzVCO1lBQ0FvQyxTQUFTO1FBQ1g7UUFDQTtZQUNFSCxNQUFNdkQscUdBQWFBO1lBQ25CeUIsT0FBTztZQUNQK0IsU0FBUyxDQUFDQyxNQUFjL0IsZ0JBQWdCK0IsSUFBSTlCLEtBQUssRUFBRThCLElBQUlFLGlCQUFpQixJQUFJRixJQUFJN0IsUUFBUTtZQUN4RjhCLFNBQVM7UUFDWDtRQUNBO1lBQ0VILE1BQU10RCxxR0FBTUE7WUFDWndCLE9BQU87WUFDUCtCLFNBQVMsQ0FBQ0MsTUFBY3ZCLFdBQVd1QixJQUFJVCxHQUFHO1lBQzFDVSxTQUFTO1FBQ1g7S0FDRDtJQUVELE1BQU1SLGVBQWU7UUFDbkIsc0NBQXNDO1FBQ3RDVSxPQUFPQyxRQUFRLENBQUNDLE1BQU07SUFDeEI7SUFFQSxNQUFNQyxnQkFBZ0I7WUFBTyxFQUFFcEMsS0FBSyxFQUFFQyxRQUFRLEVBQXVDO1FBQ25GLElBQUk7WUFDRixNQUFNUSxXQUFXLE1BQU1DLE1BQU0sR0FBbUMsT0FBaENDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLEVBQUMsbUJBQWlCO2dCQUMvRUMsUUFBUTtnQkFDUkMsU0FBU0M7Z0JBQ1RxQixNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUV2QztvQkFBT0M7Z0JBQVM7WUFDekM7WUFDQSxJQUFJLENBQUNRLFNBQVNRLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTXNCLFVBQVUsTUFBTS9CLFNBQVNnQyxJQUFJO1lBQ25DLE1BQU1ULG9CQUFvQixNQUFNVSxnQkFBZ0J6QztZQUNoRG5CLFNBQVM7bUJBQUlEO2dCQUFPO29CQUFFd0MsS0FBS21CLFFBQVFuQixHQUFHO29CQUFFckI7b0JBQU9DO29CQUFVK0I7Z0JBQWtCO2FBQUU7WUFDN0V6QyxNQUFNO2dCQUFFYyxPQUFPO2dCQUFXQyxhQUFhO1lBQStCO1lBQ3RFLDhDQUE4QztZQUM5Q2dCLFdBQVcsSUFBTUMsZ0JBQWdCO1FBQ25DLEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRQyxLQUFLLENBQUMsNEJBQTRCRjtZQUMxQ2pDLE1BQU07Z0JBQUVjLE9BQU87Z0JBQVNDLGFBQWE7WUFBMEI7UUFDakU7SUFDRjtJQUVBLG9DQUFvQztJQUNwQ3pDLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU04RTtxREFBVztvQkFDZixJQUFJLElBQTZCLEVBQUU7d0JBQ2pDLE1BQU1DLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQzt3QkFDcEMsSUFBSSxDQUFDRixRQUFROzRCQUNYdEQsT0FBT3lELElBQUksQ0FBQzs0QkFDWjt3QkFDRjt3QkFFQS9ELFNBQVM0RDt3QkFDVDFELGtCQUFrQjt3QkFFbEIsSUFBSTs0QkFDRixNQUFNNkIsVUFBVTtnQ0FDZGlDLGVBQWUsVUFBaUIsT0FBUEo7Z0NBQ3pCLGdCQUFnQjs0QkFDbEI7NEJBRUEsTUFBTUssTUFBTSxNQUFNdkMsTUFBTSxHQUFtQyxPQUFoQ0MsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDQyxtQkFBbUIsRUFBQyxXQUFTO2dDQUNsRUU7Z0NBQ0FtQyxPQUFPOzRCQUNUOzRCQUVBLElBQUksQ0FBQ0QsSUFBSWhDLEVBQUUsRUFBRTtnQ0FDWCxNQUFNLElBQUlDLE1BQU0saUJBQTRCLE9BQVgrQixJQUFJRSxNQUFNOzRCQUM3Qzs0QkFFQSxNQUFNQyxPQUFlLE1BQU1ILElBQUlSLElBQUk7NEJBQ25DaEIsUUFBUTRCLEdBQUcsQ0FBQyx1QkFBdUJEOzRCQUVuQyxNQUFNRSxPQUFPLE1BQU1DLFFBQVFDLEdBQUcsQ0FDNUJKLEtBQUtLLEdBQUc7cUVBQUMsT0FBTUMsSUFBTTt3Q0FBRSxHQUFHQSxDQUFDO3dDQUFFMUIsbUJBQW1CLE1BQU1VLGdCQUFnQmdCLEVBQUV6RCxRQUFRO29DQUFFOzs0QkFHcEZuQixTQUFTd0U7NEJBQ1Q3QixRQUFRNEIsR0FBRyxDQUFDLHVCQUF1QkMsS0FBS0ssTUFBTSxFQUFFO3dCQUNsRCxFQUFFLE9BQU9qQyxPQUFPOzRCQUNkRCxRQUFRQyxLQUFLLENBQUMsd0JBQXdCQTs0QkFDdENuQyxNQUFNO2dDQUFFYyxPQUFPO2dDQUFTQyxhQUFhOzRCQUF1Qjt3QkFDOUQsU0FBVTs0QkFDUnBCLGtCQUFrQjt3QkFDcEI7b0JBQ0Y7Z0JBQ0Y7O1lBRUF5RDtRQUNGO21DQUFHO1FBQUNyRDtLQUFPO0lBRVgsTUFBTTBCLGlCQUFpQmxELGtEQUFXQTtzREFBQyxJQUFPO2dCQUN4Q2tGLGVBQWUsVUFBZ0IsT0FBTmpFO2dCQUN6QixnQkFBZ0I7WUFDbEI7cURBQUk7UUFBQ0E7S0FBTTtJQUVYLE1BQU0yRCxrQkFBa0IsT0FBT2tCO1FBQzdCLHdEQUF3RDtRQUN4RCxJQUFJLENBQUNBLGFBQWFBLFVBQVVDLElBQUksT0FBTyxJQUFJO1lBQ3pDLE9BQU9EO1FBQ1Q7UUFFQSxvQkFBb0I7UUFDcEIsSUFBSXpFLGdCQUFnQjJFLEdBQUcsQ0FBQ0YsWUFBWTtZQUNsQyxPQUFPekUsZ0JBQWdCNEUsR0FBRyxDQUFDSDtRQUM3QjtRQUVBLElBQUk7WUFDRixNQUFNWCxNQUFNLE1BQU12QyxNQUFNLGdCQUFnQjtnQkFDdENJLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNzQixNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUV5QixtQkFBbUJKO2dCQUFVO1lBQ3REO1lBQ0EsSUFBSSxDQUFDWCxJQUFJaEMsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUM3QixNQUFNa0MsT0FBTyxNQUFNSCxJQUFJUixJQUFJO1lBRTNCLG1CQUFtQjtZQUNuQnJELG1CQUFtQjZFLENBQUFBLE9BQVEsSUFBSTVFLElBQUk0RSxNQUFNQyxHQUFHLENBQUNOLFdBQVdSLEtBQUtwQixpQkFBaUI7WUFFOUUsT0FBT29CLEtBQUtwQixpQkFBaUI7UUFDL0IsRUFBRSxPQUFPTixPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQywwQkFBMEJrQyxXQUFXbEM7WUFDbkQsT0FBT2tDO1FBQ1Q7SUFDRjtJQU1BLG9DQUFvQztJQUNwQy9GLGdEQUFTQTtvQ0FBQztZQUNSNEQsUUFBUTRCLEdBQUcsQ0FBQyx3QkFBd0J4RSxNQUFNOEUsTUFBTSxFQUFFLFNBQVMsWUFBWTs7UUFDekU7bUNBQUc7UUFBQzlFO0tBQU07SUFFVixzQ0FBc0M7SUFDdENoQixnREFBU0E7b0NBQUM7WUFDUjRELFFBQVE0QixHQUFHLENBQUMsMEJBQTBCcEUsZ0JBQWdCLFlBQVk7O1FBQ3BFO21DQUFHO1FBQUNBO0tBQWU7SUFFbkIsTUFBTSxDQUFDa0YsTUFBTUMsUUFBUSxHQUFHeEcsK0NBQVFBLENBQUM7SUFDakMsTUFBTXlHLFdBQVc7SUFFakIsTUFBTUMsY0FBYztRQUFDO1lBQUV4RSxPQUFPO1lBQW1CeUUsTUFBTTtRQUFhO1FBQUc7WUFBRXpFLE9BQU87UUFBa0I7S0FBRTtJQUVwRyxxQkFDRSw4REFBQzBFO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDekcsaUVBQU9BOzs7OzswQkFHUiw4REFBQ3dHO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ3hHLGlFQUFNQTt3QkFBQ29DLE9BQU07d0JBQWtCaUUsYUFBYUE7Ozs7OztrQ0FHN0MsOERBQUNFO3dCQUFJQyxXQUFVOztpQ0FFMEIsa0JBQ3JDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQWlDO29DQUN6QjVGLE1BQU04RSxNQUFNO29DQUFDO29DQUFhMUU7b0NBQWU7b0NBQVNGLFFBQVEsWUFBWTtrREFDM0YsOERBQUMyRjs7Ozs7b0NBQUs7b0NBQ0kvRCxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQjs7Ozs7OzswQ0FHN0MsOERBQUMzQyxvRUFBU0E7Z0NBQ1JtQyxPQUFNO2dDQUNOc0UsT0FBTzlGLE1BQU04RSxNQUFNO2dDQUNuQi9ELFNBQVNBO2dDQUNUd0QsTUFBTXZFO2dDQUNOOEMsU0FBU0E7Z0NBQ1RpRCxVQUFVLElBQU1oRyxlQUFlO2dDQUMvQmlHLGdCQUFlO2dDQUNmVixNQUFNQTtnQ0FDTkUsVUFBVUE7Z0NBQ1ZTLGNBQWNWO2dDQUNkVyxXQUFXOUY7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNakIsOERBQUNkLHdFQUFZQTtnQkFBQzZHLFFBQVFyRztnQkFBYXNHLFNBQVMsSUFBTXJHLGVBQWU7Z0JBQVFzRyxVQUFVOUM7Ozs7OzswQkFHbkYsOERBQUM3RCwrRUFBbUJBO2dCQUNsQnlHLFFBQVF0RjtnQkFDUnVGLFNBQVMsSUFBTXRGLDBCQUEwQjtnQkFDekN3RixTQUFTM0Y7Ozs7OzswQkFHWCw4REFBQ2YsMkRBQU9BOzs7Ozs7Ozs7OztBQUdkO0dBalB3QkM7O1FBT1BYLHNEQUFTQTtRQUNOUyxzREFBUUE7OztLQVJKRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGFwcFxcYWRtaW5cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXHJcbmltcG9ydCBTaWRlYmFyIGZyb20gXCJAL2NvbXBvbmVudHMvYWRtaW4vc2lkZWJhclwiXHJcbmltcG9ydCBUb3BOYXYgZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi90b3AtbmF2XCJcclxuaW1wb3J0IERhdGFUYWJsZSBmcm9tIFwiQC9jb21wb25lbnRzL2FkbWluL2RhdGEtdGFibGVcIlxyXG5pbXBvcnQgQWRkVXNlck1vZGFsIGZyb20gXCJAL2NvbXBvbmVudHMvYWRtaW4vYWRkLXVzZXItbW9kYWxcIlxyXG5pbXBvcnQgeyBFeWUsIENsaXBib2FyZENvcHksIFRyYXNoMiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5pbXBvcnQgU3R1ZGVudERldGFpbHNNb2RhbCBmcm9tIFwiQC9jb21wb25lbnRzL2FkbWluL3N0dWRlbnQtZGV0YWlscy1tb2RhbFwiXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCJcclxuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdG9hc3RlclwiXHJcblxyXG5pbnRlcmZhY2UgVXNlciB7XHJcbiAgX2lkOiBzdHJpbmdcclxuICBlbWFpbDogc3RyaW5nXHJcbiAgcGFzc3dvcmQ6IHN0cmluZ1xyXG4gIGRlY3J5cHRlZFBhc3N3b3JkPzogc3RyaW5nXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluRGFzaGJvYXJkKCkge1xyXG4gIGNvbnN0IFtpc01vZGFsT3Blbiwgc2V0SXNNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW3VzZXJzLCBzZXRVc2Vyc10gPSB1c2VTdGF0ZTxVc2VyW10+KFtdKVxyXG4gIGNvbnN0IFt0b2tlbiwgc2V0VG9rZW5dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNMb2FkaW5nVXNlcnMsIHNldElzTG9hZGluZ1VzZXJzXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtkZWNyeXB0aW9uQ2FjaGUsIHNldERlY3J5cHRpb25DYWNoZV0gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCBzdHJpbmc+PihuZXcgTWFwKCkpXHJcblxyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxyXG5cclxuICBjb25zdCBbc2VsZWN0ZWRVc2VyRm9yRGV0YWlscywgc2V0U2VsZWN0ZWRVc2VyRm9yRGV0YWlsc10gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNVc2VyRGV0YWlsc01vZGFsT3Blbiwgc2V0SXNVc2VyRGV0YWlsc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuXHJcbiAgY29uc3QgY29sdW1ucyA9IFtcclxuICAgIHsga2V5OiBcImVtYWlsXCIsIGxhYmVsOiBcIk1haWxcIiB9LFxyXG4gICAgeyBrZXk6IFwiZGVjcnlwdGVkUGFzc3dvcmRcIiwgbGFiZWw6IFwiUGFzc3dvcmRcIiB9LFxyXG4gIF1cclxuXHJcbiAgY29uc3QgY29weVVzZXJEZXRhaWxzID0gKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcclxuICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KGBFbWFpbDogJHtlbWFpbH1cXG5QYXNzd29yZDogJHtwYXNzd29yZH1gKVxyXG4gICAgdG9hc3QoeyB0aXRsZTogXCJDb3BpZWQhXCIsIGRlc2NyaXB0aW9uOiBcIlVzZXIgZGV0YWlscyBjb3BpZWQgdG8gY2xpcGJvYXJkXCIgfSlcclxuICB9XHJcblxyXG4gIGNvbnN0IGRlbGV0ZVVzZXIgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vdXNlcnM/aWQ9JHt1c2VySWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICAgICAgaGVhZGVyczogZ2V0QXV0aEhlYWRlcnMoKSxcclxuICAgICAgfSlcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIHVzZXInKVxyXG4gICAgICBzZXRVc2Vycyh1c2Vycy5maWx0ZXIodXNlciA9PiB1c2VyLl9pZCAhPT0gdXNlcklkKSlcclxuICAgICAgdG9hc3QoeyB0aXRsZTogJ1N1Y2Nlc3MnLCBkZXNjcmlwdGlvbjogJ1VzZXIgZGVsZXRlZCBzdWNjZXNzZnVsbHknIH0pXHJcbiAgICAgIC8vIFJlZnJlc2ggdGhlIHVzZXIgbGlzdCB0byBlbnN1cmUgY29uc2lzdGVuY3lcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiByZWZyZXNoVXNlcnMoKSwgMTAwMClcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIHVzZXI6JywgZXJyKVxyXG4gICAgICB0b2FzdCh7IHRpdGxlOiAnRXJyb3InLCBkZXNjcmlwdGlvbjogJ0NvdWxkIG5vdCBkZWxldGUgdXNlcicgfSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGFjdGlvbnMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGljb246IEV5ZSxcclxuICAgICAgbGFiZWw6ICdWaWV3IERldGFpbHMnLFxyXG4gICAgICBvbkNsaWNrOiAocm93OiBVc2VyKSA9PiB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRVc2VyRm9yRGV0YWlscyhyb3cpXHJcbiAgICAgICAgc2V0SXNVc2VyRGV0YWlsc01vZGFsT3Blbih0cnVlKVxyXG4gICAgICB9LFxyXG4gICAgICB2YXJpYW50OiAndmlldycgYXMgY29uc3QsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpY29uOiBDbGlwYm9hcmRDb3B5LFxyXG4gICAgICBsYWJlbDogJ0NvcHknLFxyXG4gICAgICBvbkNsaWNrOiAocm93OiBVc2VyKSA9PiBjb3B5VXNlckRldGFpbHMocm93LmVtYWlsLCByb3cuZGVjcnlwdGVkUGFzc3dvcmQgfHwgcm93LnBhc3N3b3JkKSxcclxuICAgICAgdmFyaWFudDogJ2VkaXQnIGFzIGNvbnN0LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaWNvbjogVHJhc2gyLFxyXG4gICAgICBsYWJlbDogJ0RlbGV0ZScsXHJcbiAgICAgIG9uQ2xpY2s6IChyb3c6IFVzZXIpID0+IGRlbGV0ZVVzZXIocm93Ll9pZCksXHJcbiAgICAgIHZhcmlhbnQ6ICdkZWxldGUnIGFzIGNvbnN0LFxyXG4gICAgfSxcclxuICBdXHJcblxyXG4gIGNvbnN0IHJlZnJlc2hVc2VycyA9ICgpID0+IHtcclxuICAgIC8vIEZvcmNlIGEgcGFnZSByZWxvYWQgdG8gcmVmcmVzaCBkYXRhXHJcbiAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKClcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUFkZFVzZXIgPSBhc3luYyAoeyBlbWFpbCwgcGFzc3dvcmQgfTogeyBlbWFpbDogc3RyaW5nOyBwYXNzd29yZDogc3RyaW5nIH0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vYXV0aC9yZWdpc3RlcmAsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiBnZXRBdXRoSGVhZGVycygpLFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pLFxyXG4gICAgICB9KVxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ1JlZ2lzdHJhdGlvbiBmYWlsZWQnKVxyXG4gICAgICBjb25zdCBuZXdVc2VyID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXHJcbiAgICAgIGNvbnN0IGRlY3J5cHRlZFBhc3N3b3JkID0gYXdhaXQgZGVjcnlwdFBhc3N3b3JkKHBhc3N3b3JkKVxyXG4gICAgICBzZXRVc2VycyhbLi4udXNlcnMsIHsgX2lkOiBuZXdVc2VyLl9pZCwgZW1haWwsIHBhc3N3b3JkLCBkZWNyeXB0ZWRQYXNzd29yZCB9XSlcclxuICAgICAgdG9hc3QoeyB0aXRsZTogJ1N1Y2Nlc3MnLCBkZXNjcmlwdGlvbjogJ1VzZXIgcmVnaXN0ZXJlZCBzdWNjZXNzZnVsbHknIH0pXHJcbiAgICAgIC8vIFJlZnJlc2ggdGhlIHVzZXIgbGlzdCB0byBlbnN1cmUgY29uc2lzdGVuY3lcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiByZWZyZXNoVXNlcnMoKSwgMTAwMClcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcmVnaXN0ZXIgdXNlcjonLCBlcnIpXHJcbiAgICAgIHRvYXN0KHsgdGl0bGU6ICdFcnJvcicsIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIHJlZ2lzdGVyIHVzZXInIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBUb2tlbiByZXRyaWV2YWwgYW5kIHVzZXIgZmV0Y2hpbmdcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgbG9hZERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NUb2tlbicpXHJcbiAgICAgICAgaWYgKCFzdG9yZWQpIHtcclxuICAgICAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4tbG9naW4nKVxyXG4gICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRUb2tlbihzdG9yZWQpXHJcbiAgICAgICAgc2V0SXNMb2FkaW5nVXNlcnModHJ1ZSlcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7XHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHtzdG9yZWR9YCxcclxuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS91c2Vyc2AsIHtcclxuICAgICAgICAgICAgaGVhZGVycyxcclxuICAgICAgICAgICAgY2FjaGU6ICduby1jYWNoZSdcclxuICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgaWYgKCFyZXMub2spIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBmZXRjaCBmYWlsZWQ6ICR7cmVzLnN0YXR1c31gKVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IGRhdGE6IFVzZXJbXSA9IGF3YWl0IHJlcy5qc29uKClcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwiRmV0Y2hlZCB1c2VycyBkYXRhOlwiLCBkYXRhKVxyXG5cclxuICAgICAgICAgIGNvbnN0IGxpc3QgPSBhd2FpdCBQcm9taXNlLmFsbChcclxuICAgICAgICAgICAgZGF0YS5tYXAoYXN5bmMgdSA9PiAoeyAuLi51LCBkZWNyeXB0ZWRQYXNzd29yZDogYXdhaXQgZGVjcnlwdFBhc3N3b3JkKHUucGFzc3dvcmQpIH0pKVxyXG4gICAgICAgICAgKVxyXG5cclxuICAgICAgICAgIHNldFVzZXJzKGxpc3QpXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlVzZXJzIHN0YXRlIHNldCB0bzpcIiwgbGlzdC5sZW5ndGgsIFwidXNlcnNcIilcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGxvYWRpbmcgdXNlcnM6XCIsIGVycm9yKVxyXG4gICAgICAgICAgdG9hc3QoeyB0aXRsZTogJ0Vycm9yJywgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gbG9hZCB1c2VycycgfSlcclxuICAgICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgICAgc2V0SXNMb2FkaW5nVXNlcnMoZmFsc2UpXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgbG9hZERhdGEoKVxyXG4gIH0sIFtyb3V0ZXJdKVxyXG5cclxuICBjb25zdCBnZXRBdXRoSGVhZGVycyA9IHVzZUNhbGxiYWNrKCgpID0+ICh7XHJcbiAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgfSksIFt0b2tlbl0pXHJcblxyXG4gIGNvbnN0IGRlY3J5cHRQYXNzd29yZCA9IGFzeW5jIChlbmNyeXB0ZWQ6IHN0cmluZykgPT4ge1xyXG4gICAgLy8gUmV0dXJuIGVhcmx5IGlmIG5vIGVuY3J5cHRlZCBwYXNzd29yZCBvciBlbXB0eSBzdHJpbmdcclxuICAgIGlmICghZW5jcnlwdGVkIHx8IGVuY3J5cHRlZC50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIHJldHVybiBlbmNyeXB0ZWRcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBjYWNoZSBmaXJzdFxyXG4gICAgaWYgKGRlY3J5cHRpb25DYWNoZS5oYXMoZW5jcnlwdGVkKSkge1xyXG4gICAgICByZXR1cm4gZGVjcnlwdGlvbkNhY2hlLmdldChlbmNyeXB0ZWQpIVxyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKCcvYXBpL2RlY3J5cHQnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbmNyeXB0ZWRQYXNzd29yZDogZW5jcnlwdGVkIH0pLFxyXG4gICAgICB9KVxyXG4gICAgICBpZiAoIXJlcy5vaykgdGhyb3cgbmV3IEVycm9yKCdkZWNyeXB0JylcclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKClcclxuXHJcbiAgICAgIC8vIENhY2hlIHRoZSByZXN1bHRcclxuICAgICAgc2V0RGVjcnlwdGlvbkNhY2hlKHByZXYgPT4gbmV3IE1hcChwcmV2KS5zZXQoZW5jcnlwdGVkLCBkYXRhLmRlY3J5cHRlZFBhc3N3b3JkKSlcclxuXHJcbiAgICAgIHJldHVybiBkYXRhLmRlY3J5cHRlZFBhc3N3b3JkXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdEZWNyeXB0aW9uIGZhaWxlZCBmb3I6JywgZW5jcnlwdGVkLCBlcnJvcilcclxuICAgICAgcmV0dXJuIGVuY3J5cHRlZFxyXG4gICAgfVxyXG4gIH1cclxuXHJcblxyXG5cclxuXHJcblxyXG4gIC8vIERlYnVnIGxvZyBmb3IgdXNlcnMgc3RhdGUgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIlVzZXJzIHN0YXRlIGNoYW5nZWQ6XCIsIHVzZXJzLmxlbmd0aCwgXCJ1c2Vyc1wiKSAvLyBEZWJ1ZyBsb2dcclxuICB9LCBbdXNlcnNdKVxyXG5cclxuICAvLyBEZWJ1ZyBsb2cgZm9yIGxvYWRpbmcgc3RhdGUgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIkxvYWRpbmcgc3RhdGUgY2hhbmdlZDpcIiwgaXNMb2FkaW5nVXNlcnMpIC8vIERlYnVnIGxvZ1xyXG4gIH0sIFtpc0xvYWRpbmdVc2Vyc10pXHJcblxyXG4gIGNvbnN0IFtwYWdlLCBzZXRQYWdlXSA9IHVzZVN0YXRlKDEpXHJcbiAgY29uc3QgcGFnZVNpemUgPSAxMFxyXG5cclxuICBjb25zdCBicmVhZGNydW1icyA9IFt7IGxhYmVsOiBcIkFkbWluIERhc2hib2FyZFwiLCBocmVmOiBcIi9kYXNoYm9hcmRcIiB9LCB7IGxhYmVsOiBcIlN0dWRlbnQgRGV0YWlsc1wiIH1dXHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXhcIj5cclxuICAgICAgey8qIFNpZGViYXIgKi99XHJcbiAgICAgIDxTaWRlYmFyIC8+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgey8qIFRvcCBOYXZpZ2F0aW9uICovfVxyXG4gICAgICAgIDxUb3BOYXYgdGl0bGU9XCJBZG1pbiBEYXNoYm9hcmRcIiBicmVhZGNydW1icz17YnJlYWRjcnVtYnN9IC8+XHJcblxyXG4gICAgICAgIHsvKiBDb250ZW50ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNlwiPlxyXG4gICAgICAgICAgey8qIERlYnVnIGluZm8gKi99XHJcbiAgICAgICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgcC0yIGJnLXllbGxvdy0xMDAgdGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgIERlYnVnOiB1c2Vycy5sZW5ndGg9e3VzZXJzLmxlbmd0aH0sIGlzTG9hZGluZz17aXNMb2FkaW5nVXNlcnN9LCB0b2tlbj17dG9rZW4gPyAncHJlc2VudCcgOiAnbWlzc2luZyd9XHJcbiAgICAgICAgICAgICAgPGJyIC8+XHJcbiAgICAgICAgICAgICAgQVBJIFVSTDoge3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgdGl0bGU9XCJTdHVkZW50c1wiXHJcbiAgICAgICAgICAgIGNvdW50PXt1c2Vycy5sZW5ndGh9XHJcbiAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgIGRhdGE9e3VzZXJzIGFzIHVua25vd24gYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj5bXX1cclxuICAgICAgICAgICAgYWN0aW9ucz17YWN0aW9ucyBhcyB1bmtub3duIGFzIEFycmF5PHsgaWNvbjogUmVhY3QuQ29tcG9uZW50VHlwZTx7IGNsYXNzTmFtZT86IHN0cmluZyB9PjsgbGFiZWw6IHN0cmluZzsgb25DbGljazogKHJvdzogUmVjb3JkPHN0cmluZywgdW5rbm93bj4pID0+IHZvaWQ7IHZhcmlhbnQ/OiBcImRlZmF1bHRcIiB8IFwiZWRpdFwiIHwgXCJkZWxldGVcIiB8IFwidmlld1wiIH0+fVxyXG4gICAgICAgICAgICBvbkFkZE5ldz17KCkgPT4gc2V0SXNNb2RhbE9wZW4odHJ1ZSl9XHJcbiAgICAgICAgICAgIGFkZEJ1dHRvbkxhYmVsPVwiQWRkIFVzZXJcIlxyXG4gICAgICAgICAgICBwYWdlPXtwYWdlfVxyXG4gICAgICAgICAgICBwYWdlU2l6ZT17cGFnZVNpemV9XHJcbiAgICAgICAgICAgIG9uUGFnZUNoYW5nZT17c2V0UGFnZX1cclxuICAgICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmdVc2Vyc31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIEFkZCBVc2VyIE1vZGFsICovfVxyXG4gICAgICA8QWRkVXNlck1vZGFsIGlzT3Blbj17aXNNb2RhbE9wZW59IG9uQ2xvc2U9eygpID0+IHNldElzTW9kYWxPcGVuKGZhbHNlKX0gb25TdWJtaXQ9e2hhbmRsZUFkZFVzZXJ9IC8+XHJcblxyXG4gICAgICB7LyogU3R1ZGVudCBEZXRhaWxzIE1vZGFsICovfVxyXG4gICAgICA8U3R1ZGVudERldGFpbHNNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17aXNVc2VyRGV0YWlsc01vZGFsT3Blbn1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc1VzZXJEZXRhaWxzTW9kYWxPcGVuKGZhbHNlKX1cclxuICAgICAgICBzdHVkZW50PXtzZWxlY3RlZFVzZXJGb3JEZXRhaWxzfVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgPFRvYXN0ZXIgLz5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJvdXRlciIsIlNpZGViYXIiLCJUb3BOYXYiLCJEYXRhVGFibGUiLCJBZGRVc2VyTW9kYWwiLCJFeWUiLCJDbGlwYm9hcmRDb3B5IiwiVHJhc2gyIiwiU3R1ZGVudERldGFpbHNNb2RhbCIsInVzZVRvYXN0IiwiVG9hc3RlciIsIkFkbWluRGFzaGJvYXJkIiwiaXNNb2RhbE9wZW4iLCJzZXRJc01vZGFsT3BlbiIsInVzZXJzIiwic2V0VXNlcnMiLCJ0b2tlbiIsInNldFRva2VuIiwiaXNMb2FkaW5nVXNlcnMiLCJzZXRJc0xvYWRpbmdVc2VycyIsImRlY3J5cHRpb25DYWNoZSIsInNldERlY3J5cHRpb25DYWNoZSIsIk1hcCIsInJvdXRlciIsInRvYXN0Iiwic2VsZWN0ZWRVc2VyRm9yRGV0YWlscyIsInNldFNlbGVjdGVkVXNlckZvckRldGFpbHMiLCJpc1VzZXJEZXRhaWxzTW9kYWxPcGVuIiwic2V0SXNVc2VyRGV0YWlsc01vZGFsT3BlbiIsImNvbHVtbnMiLCJrZXkiLCJsYWJlbCIsImNvcHlVc2VyRGV0YWlscyIsImVtYWlsIiwicGFzc3dvcmQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZGVsZXRlVXNlciIsInVzZXJJZCIsInJlc3BvbnNlIiwiZmV0Y2giLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIm1ldGhvZCIsImhlYWRlcnMiLCJnZXRBdXRoSGVhZGVycyIsIm9rIiwiRXJyb3IiLCJmaWx0ZXIiLCJ1c2VyIiwiX2lkIiwic2V0VGltZW91dCIsInJlZnJlc2hVc2VycyIsImVyciIsImNvbnNvbGUiLCJlcnJvciIsImFjdGlvbnMiLCJpY29uIiwib25DbGljayIsInJvdyIsInZhcmlhbnQiLCJkZWNyeXB0ZWRQYXNzd29yZCIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwiaGFuZGxlQWRkVXNlciIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibmV3VXNlciIsImpzb24iLCJkZWNyeXB0UGFzc3dvcmQiLCJsb2FkRGF0YSIsInN0b3JlZCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJwdXNoIiwiQXV0aG9yaXphdGlvbiIsInJlcyIsImNhY2hlIiwic3RhdHVzIiwiZGF0YSIsImxvZyIsImxpc3QiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwidSIsImxlbmd0aCIsImVuY3J5cHRlZCIsInRyaW0iLCJoYXMiLCJnZXQiLCJlbmNyeXB0ZWRQYXNzd29yZCIsInByZXYiLCJzZXQiLCJwYWdlIiwic2V0UGFnZSIsInBhZ2VTaXplIiwiYnJlYWRjcnVtYnMiLCJocmVmIiwiZGl2IiwiY2xhc3NOYW1lIiwiYnIiLCJjb3VudCIsIm9uQWRkTmV3IiwiYWRkQnV0dG9uTGFiZWwiLCJvblBhZ2VDaGFuZ2UiLCJpc0xvYWRpbmciLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWJtaXQiLCJzdHVkZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/student-details-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/student-details-modal.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,MessageCircle,RefreshCw,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_5__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction StudentDetailsModal(param) {\n    let { isOpen, onClose, student } = param;\n    var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_chatStats5, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_quizStats5, _analytics_quizStats6, _analytics_quizStats7, _analytics_quizStats8, _analytics_activityPattern;\n    _s();\n    console.log('StudentDetailsModal rendered with:', {\n        isOpen,\n        student: student === null || student === void 0 ? void 0 : student._id\n    });\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [studentInfo, setStudentInfo] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [subjectMap, setSubjectMap] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [subjectsLoading, setSubjectsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const modalContentRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Fetch subjects for mapping IDs to names\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"StudentDetailsModal.useEffect\": ()=>{\n            const fetchSubjects = {\n                \"StudentDetailsModal.useEffect.fetchSubjects\": async ()=>{\n                    try {\n                        setSubjectsLoading(true);\n                        const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000', \"/admin/subjects\"), {\n                            headers: getAuthHeaders()\n                        });\n                        if (response.ok) {\n                            const subjects = await response.json();\n                            const mapping = {};\n                            subjects.forEach({\n                                \"StudentDetailsModal.useEffect.fetchSubjects\": (subject)=>{\n                                    mapping[subject._id] = subject;\n                                }\n                            }[\"StudentDetailsModal.useEffect.fetchSubjects\"]);\n                            setSubjectMap(mapping);\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch subjects:', error);\n                    } finally{\n                        setSubjectsLoading(false);\n                    }\n                }\n            }[\"StudentDetailsModal.useEffect.fetchSubjects\"];\n            if (isOpen) {\n                fetchSubjects();\n            }\n        }\n    }[\"StudentDetailsModal.useEffect\"], [\n        isOpen\n    ]);\n    // Fetch student analytics when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"StudentDetailsModal.useEffect\": ()=>{\n            if (isOpen && (student === null || student === void 0 ? void 0 : student._id)) {\n                console.log('Modal opened for student:', student._id);\n                fetchStudentAnalytics();\n            }\n        }\n    }[\"StudentDetailsModal.useEffect\"], [\n        isOpen,\n        student === null || student === void 0 ? void 0 : student._id\n    ]) // fetchStudentAnalytics is stable, no need to add as dependency\n    ;\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Authorization': \"Bearer \".concat(token),\n            'Content-Type': 'application/json'\n        };\n    };\n    // Helper function to convert subject ID to name\n    const getSubjectDisplayName = (subjectId)=>{\n        const subject = subjectMap[subjectId];\n        return subject ? subject.name : subjectId // Fallback to ID if not found\n        ;\n    };\n    // Simple pie chart component for quiz distribution\n    const QuizDistributionChart = (param)=>{\n        let { data } = param;\n        const total = Object.values(data).reduce((sum, val)=>sum + val, 0);\n        if (total === 0) return null;\n        const colors = [\n            '#3B82F6',\n            '#EF4444',\n            '#10B981',\n            '#F59E0B',\n            '#8B5CF6'\n        ];\n        let currentAngle = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            \"data-chart\": \"pie\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-3\",\n                    style: {\n                        width: '150px',\n                        height: '150px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"150\",\n                        height: \"150\",\n                        viewBox: \"0 0 150 150\",\n                        className: \"transform -rotate-90\",\n                        style: {\n                            display: 'block'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"75\",\n                                cy: \"75\",\n                                r: \"60\",\n                                fill: \"none\",\n                                stroke: \"#e5e7eb\",\n                                strokeWidth: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            Object.entries(data).map((param, index)=>{\n                                let [subject, count] = param;\n                                const percentage = count / total * 100;\n                                const angle = percentage / 100 * 360;\n                                const startAngle = currentAngle;\n                                currentAngle += angle;\n                                const x1 = 75 + 60 * Math.cos(startAngle * Math.PI / 180);\n                                const y1 = 75 + 60 * Math.sin(startAngle * Math.PI / 180);\n                                const x2 = 75 + 60 * Math.cos((startAngle + angle) * Math.PI / 180);\n                                const y2 = 75 + 60 * Math.sin((startAngle + angle) * Math.PI / 180);\n                                const largeArcFlag = angle > 180 ? 1 : 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 75 75 L \".concat(x1, \" \").concat(y1, \" A 60 60 0 \").concat(largeArcFlag, \" 1 \").concat(x2, \" \").concat(y2, \" Z\"),\n                                    fill: colors[index % colors.length],\n                                    stroke: \"#ffffff\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:opacity-80 transition-opacity\"\n                                }, subject, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-2 text-xs max-w-sm\",\n                    children: Object.entries(data).map((param, index)=>{\n                        let [subject, count] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-sm\",\n                                    style: {\n                                        backgroundColor: colors[index % colors.length]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        getSubjectDisplayName(subject),\n                                        \": \",\n                                        count\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, subject, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    };\n    // Simple bar chart for quiz accuracy\n    const AccuracyBarChart = (param)=>{\n        let { data } = param;\n        if (!data || Object.keys(data).length === 0) return null;\n        const maxScore = Math.max(...Object.values(data));\n        const colors = [\n            '#3B82F6',\n            '#EF4444',\n            '#10B981',\n            '#F59E0B',\n            '#8B5CF6'\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2 p-3 bg-white rounded-lg\",\n            \"data-chart\": \"bar\",\n            style: {\n                width: '200px',\n                minHeight: '120px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"text-center font-medium text-gray-800 mb-2 text-sm\",\n                    children: \"Subject-wise Accuracy\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                Object.entries(data).map((param, index)=>{\n                    let [subject, score] = param;\n                    const percentage = Math.round(score * 100);\n                    const barWidth = maxScore > 0 ? score / maxScore * 100 : 0;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-700\",\n                                        children: getSubjectDisplayName(subject)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-900\",\n                                        children: [\n                                            percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-4 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 rounded-full transition-all duration-300 flex items-center justify-end pr-2\",\n                                    style: {\n                                        width: \"\".concat(Math.max(barWidth, 10), \"%\"),\n                                        backgroundColor: colors[index % colors.length]\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: [\n                                            percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, subject, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this);\n                })\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    };\n    const fetchStudentAnalytics = async ()=>{\n        if (!(student === null || student === void 0 ? void 0 : student._id)) return;\n        console.log('Fetching analytics for student:', student._id);\n        console.log('API URL:', \"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000', \"/admin/analytics/student/\").concat(student._id));\n        setIsLoading(true);\n        try {\n            // First, try to get user details from the existing users API\n            const userResponse = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000', \"/users\"), {\n                headers: getAuthHeaders()\n            });\n            let userDetails = null;\n            if (userResponse.ok) {\n                const users = await userResponse.json();\n                userDetails = users.find((u)=>u._id === student._id);\n                console.log('Found user details:', userDetails);\n            }\n            // Try to fetch analytics from the new endpoint\n            const response = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000', \"/admin/analytics/student/\").concat(student._id), {\n                headers: getAuthHeaders()\n            });\n            console.log('Analytics API response status:', response.status);\n            if (response.ok) {\n                const data = await response.json();\n                console.log('Analytics data received:', data);\n                console.log('Analytics structure:', JSON.stringify(data.analytics, null, 2));\n                // Set student info from API response\n                if (data.studentInfo) {\n                    setStudentInfo(data.studentInfo);\n                }\n                // Set analytics data from API\n                if (data.analytics) {\n                    console.log('Setting analytics data:', data.analytics);\n                    setAnalytics(data.analytics);\n                } else {\n                    console.log('No analytics data received, showing empty state');\n                    setAnalytics(createFallbackAnalytics());\n                }\n            } else {\n                console.log('Analytics API not available, status:', response.status);\n                // Show empty state instead of dummy data\n                setAnalytics(createFallbackAnalytics());\n                toast({\n                    title: \"Info\",\n                    description: \"Analytics service is not available. Showing empty state.\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching student analytics:', error);\n            // Show empty state instead of dummy data\n            setAnalytics(createFallbackAnalytics());\n            toast({\n                title: \"Error\",\n                description: \"Failed to load analytics data. Please try refreshing.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createFallbackAnalytics = ()=>{\n        // Return empty data when no real analytics available\n        return {\n            quizStats: {\n                totalAttempted: 0,\n                accuracy: 0,\n                subjectWiseAttempts: {},\n                averageScores: {},\n                lastQuizDate: 'No quizzes taken yet',\n                topicsCompleted: 0\n            },\n            chatStats: {\n                totalMessages: 0,\n                totalDoubts: 0,\n                mostDiscussedSubject: 'No chat activity yet',\n                totalTimeSpent: '0hr 0min',\n                timeOfDayMostActive: 'No activity yet',\n                streak: 0\n            },\n            leaderboardStats: {\n                currentRank: 0,\n                sparkPoints: 0,\n                rankMovement: 'No rank yet',\n                motivationLevel: 'Getting started'\n            },\n            activityPattern: {\n                dailyActivity: [],\n                weeklyPattern: {},\n                monthlyTrend: []\n            }\n        };\n    };\n    const generatePDFReport = async ()=>{\n        if (!modalContentRef.current || !displayStudentInfo) return;\n        try {\n            // Create a new PDF document\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_4__[\"default\"]('p', 'mm', 'a4');\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            let yPosition = 20;\n            // Add title\n            pdf.setFontSize(20);\n            pdf.setFont('helvetica', 'bold');\n            pdf.text('Student Analytics Report', pageWidth / 2, yPosition, {\n                align: 'center'\n            });\n            yPosition += 15;\n            // Add generation date\n            pdf.setFontSize(10);\n            pdf.setFont('helvetica', 'normal');\n            pdf.text(\"Generated on: \".concat(new Date().toLocaleDateString()), pageWidth / 2, yPosition, {\n                align: 'center'\n            });\n            yPosition += 20;\n            // Student Information Section\n            pdf.setFontSize(14);\n            pdf.setFont('helvetica', 'bold');\n            pdf.text('Student Information', 20, yPosition);\n            yPosition += 10;\n            pdf.setFontSize(10);\n            pdf.setFont('helvetica', 'normal');\n            const studentInfo = [\n                \"Name: \".concat(displayStudentInfo.name || 'Unknown Student'),\n                \"Email: \".concat(displayStudentInfo.email),\n                \"Phone: \".concat(displayStudentInfo.phone || 'Not provided'),\n                \"Class: \".concat(displayStudentInfo.class || 'Not specified'),\n                \"School: \".concat(displayStudentInfo.schoolName || 'Not specified'),\n                \"Registration Date: \".concat(new Date(displayStudentInfo.createdAt).toLocaleDateString()),\n                \"Subjects: \".concat(displayStudentInfo.subjects && displayStudentInfo.subjects.length > 0 ? displayStudentInfo.subjects.join(', ') : 'None specified')\n            ];\n            studentInfo.forEach((info)=>{\n                pdf.text(info, 20, yPosition);\n                yPosition += 6;\n            });\n            yPosition += 10;\n            // Analytics Section\n            if (analytics) {\n                var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_quizStats5;\n                pdf.setFontSize(14);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Performance Analytics', 20, yPosition);\n                yPosition += 10;\n                // Quiz Statistics\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Quiz Statistics', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const quizStats = [\n                    \"Quizzes Attempted: \".concat(((_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0),\n                    \"Quiz Accuracy: \".concat(((_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0, \"%\"),\n                    \"Topics Completed: \".concat(((_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0),\n                    \"Last Quiz Date: \".concat(((_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'No quizzes taken')\n                ];\n                quizStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                // Add subject-wise performance if available\n                if (((_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0) {\n                    yPosition += 5;\n                    pdf.setFontSize(11);\n                    pdf.setFont('helvetica', 'bold');\n                    pdf.text('Subject-wise Performance:', 25, yPosition);\n                    yPosition += 6;\n                    pdf.setFontSize(10);\n                    pdf.setFont('helvetica', 'normal');\n                    Object.entries(analytics.quizStats.averageScores).forEach((param)=>{\n                        let [subjectKey, score] = param;\n                        pdf.text(\"\".concat(getSubjectDisplayName(subjectKey), \": \").concat(Math.round(score * 100), \"%\"), 30, yPosition);\n                        yPosition += 5;\n                    });\n                }\n                yPosition += 10;\n                // Chat Statistics\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Chat Statistics', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const chatStats = [\n                    \"Total Messages: \".concat(((_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0),\n                    \"Total Doubts Asked: \".concat(((_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0),\n                    \"Most Discussed Subject: \".concat(((_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) ? getSubjectDisplayName(analytics.chatStats.mostDiscussedSubject) : 'No chat activity'),\n                    \"Total Time Spent: \".concat(((_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || '0hr 0min'),\n                    \"Learning Streak: \".concat(((_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.streak) || 0, \" days\")\n                ];\n                chatStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                yPosition += 10;\n                // Leaderboard Stats\n                pdf.setFontSize(12);\n                pdf.setFont('helvetica', 'bold');\n                pdf.text('Leaderboard Position', 20, yPosition);\n                yPosition += 8;\n                pdf.setFontSize(10);\n                pdf.setFont('helvetica', 'normal');\n                const leaderboardStats = [\n                    \"Current Rank: \".concat(((_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked'),\n                    \"Spark Points: \".concat(((_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0),\n                    \"Rank Movement: \".concat(((_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'No movement'),\n                    \"Motivation Level: \".concat(((_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'Getting started')\n                ];\n                leaderboardStats.forEach((stat)=>{\n                    pdf.text(stat, 25, yPosition);\n                    yPosition += 6;\n                });\n                yPosition += 15;\n                // Add charts on a new page if data exists\n                if (((_analytics_quizStats5 = analytics.quizStats) === null || _analytics_quizStats5 === void 0 ? void 0 : _analytics_quizStats5.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0) {\n                    try {\n                        // Add a new page for charts\n                        pdf.addPage();\n                        let chartYPosition = 20;\n                        // Add charts page title\n                        pdf.setFontSize(18);\n                        pdf.setFont('helvetica', 'bold');\n                        pdf.text('Performance Charts', pageWidth / 2, chartYPosition, {\n                            align: 'center'\n                        });\n                        chartYPosition += 30;\n                        // Wait a moment for charts to render properly\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        // Find chart elements\n                        const pieChartElement = document.querySelector('[data-chart=\"pie\"]');\n                        const barChartElement = document.querySelector('[data-chart=\"bar\"]');\n                        if (pieChartElement) {\n                            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(pieChartElement, {\n                                backgroundColor: '#ffffff',\n                                scale: 3,\n                                useCORS: true,\n                                allowTaint: true,\n                                width: pieChartElement.offsetWidth,\n                                height: pieChartElement.offsetHeight,\n                                logging: false\n                            });\n                            const imgData = canvas.toDataURL('image/png', 1.0);\n                            pdf.setFontSize(14);\n                            pdf.setFont('helvetica', 'bold');\n                            pdf.text('Quiz Attempts by Subject', pageWidth / 2, chartYPosition, {\n                                align: 'center'\n                            });\n                            chartYPosition += 10;\n                            // Add pie chart - smaller and properly centered\n                            const chartWidth = 60;\n                            const chartHeight = 50;\n                            const chartX = (pageWidth - chartWidth) / 2;\n                            pdf.addImage(imgData, 'PNG', chartX, chartYPosition, chartWidth, chartHeight);\n                            chartYPosition += chartHeight + 15;\n                        }\n                        if (barChartElement) {\n                            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(barChartElement, {\n                                backgroundColor: '#ffffff',\n                                scale: 3,\n                                useCORS: true,\n                                allowTaint: true,\n                                width: barChartElement.offsetWidth,\n                                height: barChartElement.offsetHeight,\n                                logging: false\n                            });\n                            const imgData = canvas.toDataURL('image/png', 1.0);\n                            pdf.setFontSize(14);\n                            pdf.setFont('helvetica', 'bold');\n                            pdf.text('Subject-wise Accuracy Performance', pageWidth / 2, chartYPosition, {\n                                align: 'center'\n                            });\n                            chartYPosition += 10;\n                            // Add bar chart - smaller and properly centered\n                            const chartWidth = 80;\n                            const chartHeight = 60;\n                            const chartX = (pageWidth - chartWidth) / 2;\n                            pdf.addImage(imgData, 'PNG', chartX, chartYPosition, chartWidth, chartHeight);\n                        }\n                    } catch (chartError) {\n                        console.error('Error adding charts to PDF:', chartError);\n                        // Add text note about charts\n                        pdf.setFontSize(12);\n                        pdf.text('Charts could not be generated. Please view them in the web interface.', 20, 50);\n                    }\n                }\n            }\n            // Save the PDF\n            const fileName = \"student-report-\".concat(displayStudentInfo.name || (student === null || student === void 0 ? void 0 : student.email.split('@')[0]) || 'unknown', \"-\").concat(new Date().toISOString().split('T')[0], \".pdf\");\n            pdf.save(fileName);\n            toast({\n                title: \"Download Complete\",\n                description: \"Student report has been downloaded as PDF with charts.\"\n            });\n        } catch (error) {\n            console.error('Error generating PDF:', error);\n            throw error;\n        }\n    };\n    const handleDownload = async ()=>{\n        if (!(student === null || student === void 0 ? void 0 : student._id)) return;\n        setIsDownloading(true);\n        try {\n            await generatePDFReport();\n        } catch (error) {\n            console.error('Error downloading report:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to generate PDF report. Please try again.\"\n            });\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Helper function to generate text report\n    const generateTextReport = (studentInfo, analytics)=>{\n        var _analytics_quizStats, _analytics_quizStats1, _analytics_quizStats2, _analytics_quizStats3, _analytics_quizStats4, _analytics_chatStats, _analytics_chatStats1, _analytics_chatStats2, _analytics_chatStats3, _analytics_chatStats4, _analytics_chatStats5, _analytics_leaderboardStats, _analytics_leaderboardStats1, _analytics_leaderboardStats2, _analytics_leaderboardStats3, _analytics_activityPattern;\n        const reportDate = new Date().toLocaleDateString();\n        const studentName = studentInfo.name || 'Unknown Student';\n        return \"\\nSTUDYBUDDY STUDENT REPORT\\nGenerated on: \".concat(reportDate, \"\\n\\nSTUDENT INFORMATION\\n==================\\nName: \").concat(studentName, \"\\nEmail: \").concat(studentInfo.email, \"\\nPhone: \").concat(studentInfo.phone || 'Not provided', \"\\nClass: \").concat(studentInfo.class || 'Not specified', \"\\nSchool: \").concat(studentInfo.schoolName || 'Not specified', \"\\nRegistration Date: \").concat(new Date(studentInfo.createdAt).toLocaleDateString(), \"\\nSubjects: \").concat(studentInfo.subjects && studentInfo.subjects.length > 0 ? studentInfo.subjects.join(', ') : 'None specified', \"\\n\\nPERFORMANCE ANALYTICS\\n====================\\n\").concat(analytics ? \"\\nQUIZ STATISTICS\\n--------------\\nQuizzes Attempted: \".concat(((_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0, \"\\nQuiz Accuracy: \").concat(((_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0, \"%\\nTopics Completed: \").concat(((_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0, \"\\nLast Quiz Date: \").concat(((_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'No quizzes taken', \"\\n\\nSUBJECT SCORES\\n--------------\\n\").concat(((_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 ? Object.entries(analytics.quizStats.averageScores).map((param)=>{\n            let [subject, score] = param;\n            return \"\".concat(subject, \": \").concat(Math.round(score * 100), \"%\");\n        }).join('\\n') : 'No quiz scores available', \"\\n\\nCHAT STATISTICS\\n--------------\\nTotal Messages Sent: \").concat(((_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0, \"\\nTotal Doubts Asked: \").concat(((_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0, \"\\nMost Discussed Subject: \").concat(((_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) || 'No chat activity', \"\\nTotal Time Spent: \").concat(((_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || '0hr 0min', \"\\nMost Active Time: \").concat(((_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.timeOfDayMostActive) || 'No activity', \"\\nLearning Streak: \").concat(((_analytics_chatStats5 = analytics.chatStats) === null || _analytics_chatStats5 === void 0 ? void 0 : _analytics_chatStats5.streak) || 0, \" days\\n\\nLEADERBOARD STATS\\n----------------\\nCurrent Rank: \").concat(((_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked', \"\\nSpark Points: \").concat(((_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0, \"\\nRank Movement: \").concat(((_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'No movement', \"\\nMotivation Level: \").concat(((_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'Getting started', \"\\n\\nRECENT ACTIVITY\\n--------------\\n\").concat(((_analytics_activityPattern = analytics.activityPattern) === null || _analytics_activityPattern === void 0 ? void 0 : _analytics_activityPattern.dailyActivity) && analytics.activityPattern.dailyActivity.length > 0 ? analytics.activityPattern.dailyActivity.slice(-7).map((activity)=>\"\".concat(new Date(activity.date).toLocaleDateString(), \": \").concat(activity.queries, \" queries, \").concat(activity.timeSpent, \" min, Subjects: \").concat(activity.subjects.map((subjectId)=>getSubjectDisplayName(subjectId)).join(', ') || 'None')).join('\\n') : 'No recent activity', \"\\n\") : 'Analytics data not available', \"\\n\\nReport generated by StudyBuddy Admin Dashboard\\n    \").trim();\n    };\n    if (!isOpen || !student) return null;\n    // Enhanced student info with better fallback data\n    const displayStudentInfo = studentInfo || {\n        userId: student._id,\n        name: student.name || \"Student \".concat(student.email.split('@')[0]),\n        phone: student.phone || \"Not provided\",\n        class: student.class || \"Not specified\",\n        schoolName: student.schoolName || \"Not specified\",\n        email: student.email,\n        createdAt: student.createdAt || student.createdOn || new Date().toISOString(),\n        subjects: student.subjects || [],\n        profileImage: student.avatar || student.profileImage || undefined\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[95vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-6 h-6 text-primary-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Student Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    onClick: fetchStudentAnalytics,\n                                    disabled: isLoading,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isDownloading,\n                                    className: \"bg-primary-blue hover:bg-primary-blue/90 text-white px-4 py-2 text-sm flex items-center\",\n                                    children: [\n                                        isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        isDownloading ? 'Generating...' : 'Download Details'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 698,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: modalContentRef,\n                    className: \"p-6 space-y-8\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 animate-spin text-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Loading student analytics...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            analytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: studentInfo ? 'Live data from analytics service' : 'Sample data - Analytics service unavailable'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-dark rounded-lg p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-32 h-32 bg-primary-blue rounded-lg flex items-center justify-center overflow-hidden\",\n                                            children: displayStudentInfo.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: displayStudentInfo.profileImage,\n                                                alt: displayStudentInfo.name,\n                                                className: \"w-full h-full object-cover rounded-lg\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = 'none';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-16 h-16 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Class\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.class\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"School Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.schoolName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: displayStudentInfo.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-sm\",\n                                                                            children: \"Created On\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: new Date(displayStudentInfo.createdAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-sm\",\n                                                                            children: \"Subjects\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: displayStudentInfo.subjects && displayStudentInfo.subjects.length > 0 ? displayStudentInfo.subjects.join(', ') : 'Not specified'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Student Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Quiz Performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Quizzes Attempted:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats = analytics.quizStats) === null || _analytics_quizStats === void 0 ? void 0 : _analytics_quizStats.totalAttempted) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Quiz Accuracy:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats1 = analytics.quizStats) === null || _analytics_quizStats1 === void 0 ? void 0 : _analytics_quizStats1.accuracy) || 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Topics Completed:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats2 = analytics.quizStats) === null || _analytics_quizStats2 === void 0 ? void 0 : _analytics_quizStats2.topicsCompleted) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 830,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Last Quiz Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats3 = analytics.quizStats) === null || _analytics_quizStats3 === void 0 ? void 0 : _analytics_quizStats3.lastQuizDate) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_quizStats4 = analytics.quizStats) === null || _analytics_quizStats4 === void 0 ? void 0 : _analytics_quizStats4.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 && analytics.quizStats.totalAttempted > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Subject-wise Performance:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 space-y-1\",\n                                                                children: Object.entries(analytics.quizStats.averageScores).map((param)=>{\n                                                                    let [subjectKey, score] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600 capitalize\",\n                                                                                children: [\n                                                                                    getSubjectDisplayName(subjectKey),\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    Math.round(score * 100),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, subjectKey, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Chat Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Messages:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats = analytics.chatStats) === null || _analytics_chatStats === void 0 ? void 0 : _analytics_chatStats.totalMessages) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Doubts Asked:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats1 = analytics.chatStats) === null || _analytics_chatStats1 === void 0 ? void 0 : _analytics_chatStats1.totalDoubts) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Most Discussed Subject:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats2 = analytics.chatStats) === null || _analytics_chatStats2 === void 0 ? void 0 : _analytics_chatStats2.mostDiscussedSubject) ? getSubjectDisplayName(analytics.chatStats.mostDiscussedSubject) : 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Total Time Spent:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats3 = analytics.chatStats) === null || _analytics_chatStats3 === void 0 ? void 0 : _analytics_chatStats3.totalTimeSpent) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Most Active Time:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats4 = analytics.chatStats) === null || _analytics_chatStats4 === void 0 ? void 0 : _analytics_chatStats4.timeOfDayMostActive) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 879,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Learning Streak:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    (analytics === null || analytics === void 0 ? void 0 : (_analytics_chatStats5 = analytics.chatStats) === null || _analytics_chatStats5 === void 0 ? void 0 : _analytics_chatStats5.streak) || 0,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Leaderboard Position\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Current Rank:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats = analytics.leaderboardStats) === null || _analytics_leaderboardStats === void 0 ? void 0 : _analytics_leaderboardStats.currentRank) ? \"#\".concat(analytics.leaderboardStats.currentRank) : 'Not ranked'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Spark Points:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats1 = analytics.leaderboardStats) === null || _analytics_leaderboardStats1 === void 0 ? void 0 : _analytics_leaderboardStats1.sparkPoints) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Rank Movement:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats2 = analytics.leaderboardStats) === null || _analytics_leaderboardStats2 === void 0 ? void 0 : _analytics_leaderboardStats2.rankMovement) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Motivation Level:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (analytics === null || analytics === void 0 ? void 0 : (_analytics_leaderboardStats3 = analytics.leaderboardStats) === null || _analytics_leaderboardStats3 === void 0 ? void 0 : _analytics_leaderboardStats3.motivationLevel) || 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 11\n                            }, this),\n                            analytics && (((_analytics_quizStats5 = analytics.quizStats) === null || _analytics_quizStats5 === void 0 ? void 0 : _analytics_quizStats5.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0 || ((_analytics_quizStats6 = analytics.quizStats) === null || _analytics_quizStats6 === void 0 ? void 0 : _analytics_quizStats6.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Performance Charts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            ((_analytics_quizStats7 = analytics.quizStats) === null || _analytics_quizStats7 === void 0 ? void 0 : _analytics_quizStats7.subjectWiseAttempts) && Object.keys(analytics.quizStats.subjectWiseAttempts).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-4\",\n                                                        children: \"Quiz Attempts by Subject\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuizDistributionChart, {\n                                                        data: analytics.quizStats.subjectWiseAttempts\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, this),\n                                            ((_analytics_quizStats8 = analytics.quizStats) === null || _analytics_quizStats8 === void 0 ? void 0 : _analytics_quizStats8.averageScores) && Object.keys(analytics.quizStats.averageScores).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-4\",\n                                                        children: \"Subject-wise Accuracy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AccuracyBarChart, {\n                                                        data: analytics.quizStats.averageScores\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 13\n                            }, this),\n                            (analytics === null || analytics === void 0 ? void 0 : (_analytics_activityPattern = analytics.activityPattern) === null || _analytics_activityPattern === void 0 ? void 0 : _analytics_activityPattern.dailyActivity) && analytics.activityPattern.dailyActivity.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: analytics.activityPattern.dailyActivity.slice(-7).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                        children: new Date(activity.date).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-xs text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Queries: \",\n                                                                    activity.queries\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Time: \",\n                                                                    activity.timeSpent,\n                                                                    \" min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 953,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Subjects: \",\n                                                                    activity.subjects.map((subjectId)=>getSubjectDisplayName(subjectId)).join(', ') || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, this) : !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No Activity Data Available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Activity data will appear once the student starts using the platform.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            onClick: fetchStudentAnalytics,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex items-center mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_MessageCircle_RefreshCw_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh Data\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n                    lineNumber: 733,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n            lineNumber: 696,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\admin\\\\student-details-modal.tsx\",\n        lineNumber: 695,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentDetailsModal, \"6Xod99eRaXsIVofbq5cA2TGRCRk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = StudentDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"StudentDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL3N0dWRlbnQtZGV0YWlscy1tb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1GO0FBQ3BDO0FBQ0g7QUFDTztBQUMxQjtBQUNZO0FBcUZ0QixTQUFTYSxvQkFBb0IsS0FBc0Q7UUFBdEQsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBNEIsR0FBdEQ7UUEwdEJ2QkMsc0JBS3NDQSx1QkFJQUEsdUJBSUFBLHVCQUUxQ0EsdUJBc0IwQ0Esc0JBSUFBLHVCQUt0Q0EsdUJBTXNDQSx1QkFJQUEsdUJBSUFBLHVCQVV0Q0EsNkJBS3NDQSw4QkFJQUEsOEJBSUFBLDhCQVE5Q0EsdUJBQ0FBLHVCQU1JQSx1QkFTQUEsdUJBWU5BOztJQWgxQlRDLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0M7UUFBRUw7UUFBUUUsT0FBTyxFQUFFQSxvQkFBQUEsOEJBQUFBLFFBQVNJLEdBQUc7SUFBQztJQUNsRixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHZCwwREFBUUE7SUFDMUIsTUFBTSxDQUFDVSxXQUFXSyxhQUFhLEdBQUdkLCtDQUFRQSxDQUEwQjtJQUVwRSxNQUFNLENBQUNlLGFBQWFDLGVBQWUsR0FBR2hCLCtDQUFRQSxDQUFxQjtJQUNuRSxNQUFNLENBQUNpQixXQUFXQyxhQUFhLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNtQixlQUFlQyxpQkFBaUIsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FCLFlBQVlDLGNBQWMsR0FBR3RCLCtDQUFRQSxDQUEyQixDQUFDO0lBQ3hFLE1BQU0sQ0FBQ3VCLGlCQUFpQkMsbUJBQW1CLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNeUIsa0JBQWtCdkIsNkNBQU1BLENBQWlCO0lBRS9DLDBDQUEwQztJQUMxQ0QsZ0RBQVNBO3lDQUFDO1lBQ1IsTUFBTXlCOytEQUFnQjtvQkFDcEIsSUFBSTt3QkFDRkYsbUJBQW1CO3dCQUNuQixNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBOEQsT0FBM0RDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUkseUJBQXdCLG9CQUFrQjs0QkFDM0dDLFNBQVNDO3dCQUNYO3dCQUVBLElBQUlOLFNBQVNPLEVBQUUsRUFBRTs0QkFDZixNQUFNQyxXQUFXLE1BQU1SLFNBQVNTLElBQUk7NEJBQ3BDLE1BQU1DLFVBQW9DLENBQUM7NEJBQzNDRixTQUFTRyxPQUFPOytFQUFDLENBQUNDO29DQUNoQkYsT0FBTyxDQUFDRSxRQUFRM0IsR0FBRyxDQUFDLEdBQUcyQjtnQ0FDekI7OzRCQUNBakIsY0FBY2U7d0JBQ2hCO29CQUNGLEVBQUUsT0FBT0csT0FBTzt3QkFDZDlCLFFBQVE4QixLQUFLLENBQUMsNkJBQTZCQTtvQkFDN0MsU0FBVTt3QkFDUmhCLG1CQUFtQjtvQkFDckI7Z0JBQ0Y7O1lBRUEsSUFBSWxCLFFBQVE7Z0JBQ1ZvQjtZQUNGO1FBQ0Y7d0NBQUc7UUFBQ3BCO0tBQU87SUFFWCwyQ0FBMkM7SUFDM0NMLGdEQUFTQTt5Q0FBQztZQUNSLElBQUlLLFdBQVVFLG9CQUFBQSw4QkFBQUEsUUFBU0ksR0FBRyxHQUFFO2dCQUMxQkYsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QkgsUUFBUUksR0FBRztnQkFDcEQ2QjtZQUNGO1FBQ0Y7d0NBQUc7UUFBQ25DO1FBQVFFLG9CQUFBQSw4QkFBQUEsUUFBU0ksR0FBRztLQUFDLEVBQUUsZ0VBQWdFOztJQUUzRixNQUFNcUIsaUJBQWlCO1FBQ3JCLE1BQU1TLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztRQUNuQyxPQUFPO1lBQ0wsaUJBQWlCLFVBQWdCLE9BQU5GO1lBQzNCLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1HLHdCQUF3QixDQUFDQztRQUM3QixNQUFNUCxVQUFVbEIsVUFBVSxDQUFDeUIsVUFBVTtRQUNyQyxPQUFPUCxVQUFVQSxRQUFRUSxJQUFJLEdBQUdELFVBQVUsOEJBQThCOztJQUMxRTtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNRSx3QkFBd0I7WUFBQyxFQUFFQyxJQUFJLEVBQW9DO1FBQ3ZFLE1BQU1DLFFBQVFDLE9BQU9DLE1BQU0sQ0FBQ0gsTUFBTUksTUFBTSxDQUFDLENBQUNDLEtBQUtDLE1BQVFELE1BQU1DLEtBQUs7UUFDbEUsSUFBSUwsVUFBVSxHQUFHLE9BQU87UUFFeEIsTUFBTU0sU0FBUztZQUFDO1lBQVc7WUFBVztZQUFXO1lBQVc7U0FBVTtRQUN0RSxJQUFJQyxlQUFlO1FBRW5CLHFCQUNFLDhEQUFDQztZQUFJQyxXQUFVO1lBQTZCQyxjQUFXOzs4QkFDckQsOERBQUNGO29CQUFJQyxXQUFVO29CQUFnQkUsT0FBTzt3QkFBRUMsT0FBTzt3QkFBU0MsUUFBUTtvQkFBUTs4QkFDdEUsNEVBQUNDO3dCQUNDRixPQUFNO3dCQUNOQyxRQUFPO3dCQUNQRSxTQUFRO3dCQUNSTixXQUFVO3dCQUNWRSxPQUFPOzRCQUFFSyxTQUFTO3dCQUFROzswQ0FHMUIsOERBQUNDO2dDQUNDQyxJQUFHO2dDQUNIQyxJQUFHO2dDQUNIQyxHQUFFO2dDQUNGQyxNQUFLO2dDQUNMQyxRQUFPO2dDQUNQQyxhQUFZOzs7Ozs7NEJBRWJ0QixPQUFPdUIsT0FBTyxDQUFDekIsTUFBTTBCLEdBQUcsQ0FBQyxRQUFtQkM7b0NBQWxCLENBQUNyQyxTQUFTc0MsTUFBTTtnQ0FDekMsTUFBTUMsYUFBYSxRQUFTNUIsUUFBUztnQ0FDckMsTUFBTTZCLFFBQVEsYUFBYyxNQUFPO2dDQUNuQyxNQUFNQyxhQUFhdkI7Z0NBQ25CQSxnQkFBZ0JzQjtnQ0FFaEIsTUFBTUUsS0FBSyxLQUFLLEtBQUtDLEtBQUtDLEdBQUcsQ0FBQyxhQUFjRCxLQUFLRSxFQUFFLEdBQUk7Z0NBQ3ZELE1BQU1DLEtBQUssS0FBSyxLQUFLSCxLQUFLSSxHQUFHLENBQUMsYUFBY0osS0FBS0UsRUFBRSxHQUFJO2dDQUN2RCxNQUFNRyxLQUFLLEtBQUssS0FBS0wsS0FBS0MsR0FBRyxDQUFDLENBQUVILGFBQWFELEtBQUksSUFBS0csS0FBS0UsRUFBRSxHQUFJO2dDQUNqRSxNQUFNSSxLQUFLLEtBQUssS0FBS04sS0FBS0ksR0FBRyxDQUFDLENBQUVOLGFBQWFELEtBQUksSUFBS0csS0FBS0UsRUFBRSxHQUFJO2dDQUVqRSxNQUFNSyxlQUFlVixRQUFRLE1BQU0sSUFBSTtnQ0FFdkMscUJBQ0UsOERBQUNXO29DQUVDQyxHQUFHLGFBQW1CTixPQUFOSixJQUFHLEtBQW1CUSxPQUFoQkosSUFBRyxlQUErQkUsT0FBbEJFLGNBQWEsT0FBV0QsT0FBTkQsSUFBRyxLQUFNLE9BQUhDLElBQUc7b0NBQ2pFakIsTUFBTWYsTUFBTSxDQUFDb0IsUUFBUXBCLE9BQU9vQyxNQUFNLENBQUM7b0NBQ25DcEIsUUFBTztvQ0FDUEMsYUFBWTtvQ0FDWmQsV0FBVTttQ0FMTHBCOzs7Ozs0QkFRWDs7Ozs7Ozs7Ozs7OzhCQUdKLDhEQUFDbUI7b0JBQUlDLFdBQVU7OEJBQ1pSLE9BQU91QixPQUFPLENBQUN6QixNQUFNMEIsR0FBRyxDQUFDLFFBQW1CQzs0QkFBbEIsQ0FBQ3JDLFNBQVNzQyxNQUFNOzZDQUN6Qyw4REFBQ25COzRCQUFrQkMsV0FBVTs7OENBQzNCLDhEQUFDRDtvQ0FDQ0MsV0FBVTtvQ0FDVkUsT0FBTzt3Q0FBRWdDLGlCQUFpQnJDLE1BQU0sQ0FBQ29CLFFBQVFwQixPQUFPb0MsTUFBTSxDQUFDO29DQUFDOzs7Ozs7OENBRTFELDhEQUFDRTtvQ0FBS25DLFdBQVU7O3dDQUFlZCxzQkFBc0JOO3dDQUFTO3dDQUFHc0M7Ozs7Ozs7OzJCQUx6RHRDOzs7Ozs7Ozs7Ozs7Ozs7OztJQVdwQjtJQUVBLHFDQUFxQztJQUNyQyxNQUFNd0QsbUJBQW1CO1lBQUMsRUFBRTlDLElBQUksRUFBb0M7UUFDbEUsSUFBSSxDQUFDQSxRQUFRRSxPQUFPNkMsSUFBSSxDQUFDL0MsTUFBTTJDLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFFcEQsTUFBTUssV0FBV2YsS0FBS2dCLEdBQUcsSUFBSS9DLE9BQU9DLE1BQU0sQ0FBQ0g7UUFDM0MsTUFBTU8sU0FBUztZQUFDO1lBQVc7WUFBVztZQUFXO1lBQVc7U0FBVTtRQUV0RSxxQkFDRSw4REFBQ0U7WUFBSUMsV0FBVTtZQUFvQ0MsY0FBVztZQUFNQyxPQUFPO2dCQUFFQyxPQUFPO2dCQUFTcUMsV0FBVztZQUFROzs4QkFDOUcsOERBQUNDO29CQUFHekMsV0FBVTs4QkFBcUQ7Ozs7OztnQkFDbEVSLE9BQU91QixPQUFPLENBQUN6QixNQUFNMEIsR0FBRyxDQUFDLFFBQW1CQzt3QkFBbEIsQ0FBQ3JDLFNBQVM4RCxNQUFNO29CQUN6QyxNQUFNdkIsYUFBYUksS0FBS29CLEtBQUssQ0FBQ0QsUUFBUTtvQkFDdEMsTUFBTUUsV0FBV04sV0FBVyxJQUFJLFFBQVNBLFdBQVksTUFBTTtvQkFDM0QscUJBQ0UsOERBQUN2Qzt3QkFBa0JDLFdBQVU7OzBDQUMzQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDbUM7d0NBQUtuQyxXQUFVO2tEQUFpQmQsc0JBQXNCTjs7Ozs7O2tEQUN2RCw4REFBQ3VEO3dDQUFLbkMsV0FBVTs7NENBQWlCbUI7NENBQVc7Ozs7Ozs7Ozs7Ozs7MENBRTlDLDhEQUFDcEI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUNDQyxXQUFVO29DQUNWRSxPQUFPO3dDQUNMQyxPQUFPLEdBQTBCLE9BQXZCb0IsS0FBS2dCLEdBQUcsQ0FBQ0ssVUFBVSxLQUFJO3dDQUNqQ1YsaUJBQWlCckMsTUFBTSxDQUFDb0IsUUFBUXBCLE9BQU9vQyxNQUFNLENBQUM7b0NBQ2hEOzhDQUVBLDRFQUFDRTt3Q0FBS25DLFdBQVU7OzRDQUFnQ21COzRDQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBYnZEdkM7Ozs7O2dCQWtCZDs7Ozs7OztJQUdOO0lBRUEsTUFBTUUsd0JBQXdCO1FBQzVCLElBQUksRUFBQ2pDLG9CQUFBQSw4QkFBQUEsUUFBU0ksR0FBRyxHQUFFO1FBRW5CRixRQUFRQyxHQUFHLENBQUMsbUNBQW1DSCxRQUFRSSxHQUFHO1FBQzFERixRQUFRQyxHQUFHLENBQUMsWUFBWSxHQUF5RkgsT0FBdEZxQixPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJLHlCQUF3Qiw2QkFBdUMsT0FBWnZCLFFBQVFJLEdBQUc7UUFFNUhNLGFBQWE7UUFDYixJQUFJO1lBRUYsNkRBQTZEO1lBQzdELE1BQU1zRixlQUFlLE1BQU01RSxNQUFNLEdBQThELE9BQTNEQyxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJLHlCQUF3QixXQUFTO2dCQUN0R0MsU0FBU0M7WUFDWDtZQUVBLElBQUl3RSxjQUFjO1lBQ2xCLElBQUlELGFBQWF0RSxFQUFFLEVBQUU7Z0JBQ25CLE1BQU13RSxRQUFRLE1BQU1GLGFBQWFwRSxJQUFJO2dCQUNyQ3FFLGNBQWNDLE1BQU1DLElBQUksQ0FBQyxDQUFDQyxJQUF1QkEsRUFBRWhHLEdBQUcsS0FBS0osUUFBUUksR0FBRztnQkFDdEVGLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI4RjtZQUNyQztZQUVBLCtDQUErQztZQUMvQyxNQUFNOUUsV0FBVyxNQUFNQyxNQUFNLEdBQXlGcEIsT0FBdEZxQixPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJLHlCQUF3Qiw2QkFBdUMsT0FBWnZCLFFBQVFJLEdBQUcsR0FBSTtnQkFDbklvQixTQUFTQztZQUNYO1lBRUF2QixRQUFRQyxHQUFHLENBQUMsa0NBQWtDZ0IsU0FBU2tGLE1BQU07WUFFN0QsSUFBSWxGLFNBQVNPLEVBQUUsRUFBRTtnQkFDZixNQUFNZSxPQUFPLE1BQU10QixTQUFTUyxJQUFJO2dCQUNoQzFCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJzQztnQkFDeEN2QyxRQUFRQyxHQUFHLENBQUMsd0JBQXdCbUcsS0FBS0MsU0FBUyxDQUFDOUQsS0FBS3hDLFNBQVMsRUFBRSxNQUFNO2dCQUV6RSxxQ0FBcUM7Z0JBQ3JDLElBQUl3QyxLQUFLbEMsV0FBVyxFQUFFO29CQUNwQkMsZUFBZWlDLEtBQUtsQyxXQUFXO2dCQUNqQztnQkFFQSw4QkFBOEI7Z0JBQzlCLElBQUlrQyxLQUFLeEMsU0FBUyxFQUFFO29CQUNsQkMsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQnNDLEtBQUt4QyxTQUFTO29CQUNyREssYUFBYW1DLEtBQUt4QyxTQUFTO2dCQUM3QixPQUFPO29CQUNMQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1pHLGFBQWFrRztnQkFDZjtZQUdGLE9BQU87Z0JBQ0x0RyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDZ0IsU0FBU2tGLE1BQU07Z0JBQ25FLHlDQUF5QztnQkFDekMvRixhQUFha0c7Z0JBRWJuRyxNQUFNO29CQUNKb0csT0FBTztvQkFDUEMsYUFBYTtnQkFDZjtZQUNGO1FBRUYsRUFBRSxPQUFPMUUsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyxxQ0FBcUNBO1lBRW5ELHlDQUF5QztZQUN6QzFCLGFBQWFrRztZQUVibkcsTUFBTTtnQkFDSm9HLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtRQUNGLFNBQVU7WUFDUmhHLGFBQWE7UUFDZjtJQUNGO0lBSUEsTUFBTThGLDBCQUEwQjtRQUM5QixxREFBcUQ7UUFDckQsT0FBTztZQUNMRyxXQUFXO2dCQUNUQyxnQkFBZ0I7Z0JBQ2hCQyxVQUFVO2dCQUNWQyxxQkFBcUIsQ0FBQztnQkFDdEJDLGVBQWUsQ0FBQztnQkFDaEJDLGNBQWM7Z0JBQ2RDLGlCQUFpQjtZQUNuQjtZQUNBQyxXQUFXO2dCQUNUQyxlQUFlO2dCQUNmQyxhQUFhO2dCQUNiQyxzQkFBc0I7Z0JBQ3RCQyxnQkFBZ0I7Z0JBQ2hCQyxxQkFBcUI7Z0JBQ3JCQyxRQUFRO1lBQ1Y7WUFDQUMsa0JBQWtCO2dCQUNoQkMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsY0FBYztnQkFDZEMsaUJBQWlCO1lBQ25CO1lBQ0FDLGlCQUFpQjtnQkFDZkMsZUFBZSxFQUFFO2dCQUNqQkMsZUFBZSxDQUFDO2dCQUNoQkMsY0FBYyxFQUFFO1lBQ2xCO1FBQ0Y7SUFDRjtJQUlBLE1BQU1DLG9CQUFvQjtRQUN4QixJQUFJLENBQUNqSCxnQkFBZ0JrSCxPQUFPLElBQUksQ0FBQ0Msb0JBQW9CO1FBRXJELElBQUk7WUFDRiw0QkFBNEI7WUFDNUIsTUFBTUMsTUFBTSxJQUFJMUksNkNBQUtBLENBQUMsS0FBSyxNQUFNO1lBQ2pDLE1BQU0ySSxZQUFZRCxJQUFJRSxRQUFRLENBQUNDLFFBQVEsQ0FBQ0MsUUFBUTtZQUNoRCxJQUFJQyxZQUFZO1lBRWhCLFlBQVk7WUFDWkwsSUFBSU0sV0FBVyxDQUFDO1lBQ2hCTixJQUFJTyxPQUFPLENBQUMsYUFBYTtZQUN6QlAsSUFBSVEsSUFBSSxDQUFDLDRCQUE0QlAsWUFBWSxHQUFHSSxXQUFXO2dCQUFFSSxPQUFPO1lBQVM7WUFDakZKLGFBQWE7WUFFYixzQkFBc0I7WUFDdEJMLElBQUlNLFdBQVcsQ0FBQztZQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7WUFDekJQLElBQUlRLElBQUksQ0FBQyxpQkFBaUQsT0FBaEMsSUFBSUUsT0FBT0Msa0JBQWtCLEtBQU1WLFlBQVksR0FBR0ksV0FBVztnQkFBRUksT0FBTztZQUFTO1lBQ3pHSixhQUFhO1lBRWIsOEJBQThCO1lBQzlCTCxJQUFJTSxXQUFXLENBQUM7WUFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhO1lBQ3pCUCxJQUFJUSxJQUFJLENBQUMsdUJBQXVCLElBQUlIO1lBQ3BDQSxhQUFhO1lBRWJMLElBQUlNLFdBQVcsQ0FBQztZQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7WUFDekIsTUFBTXJJLGNBQWM7Z0JBQ2pCLFNBQXFELE9BQTdDNkgsbUJBQW1CN0YsSUFBSSxJQUFJO2dCQUNuQyxVQUFrQyxPQUF6QjZGLG1CQUFtQmEsS0FBSztnQkFDakMsVUFBb0QsT0FBM0NiLG1CQUFtQmMsS0FBSyxJQUFJO2dCQUNyQyxVQUFxRCxPQUE1Q2QsbUJBQW1CZSxLQUFLLElBQUk7Z0JBQ3JDLFdBQTJELE9BQWpEZixtQkFBbUJnQixVQUFVLElBQUk7Z0JBQzNDLHNCQUFpRixPQUE1RCxJQUFJTCxLQUFLWCxtQkFBbUJpQixTQUFTLEVBQUVMLGtCQUFrQjtnQkFDOUUsYUFBOEksT0FBbElaLG1CQUFtQnpHLFFBQVEsSUFBSXlHLG1CQUFtQnpHLFFBQVEsQ0FBQ3lELE1BQU0sR0FBRyxJQUFJZ0QsbUJBQW1CekcsUUFBUSxDQUFDMkgsSUFBSSxDQUFDLFFBQVE7YUFDL0g7WUFFRC9JLFlBQVl1QixPQUFPLENBQUN5SCxDQUFBQTtnQkFDbEJsQixJQUFJUSxJQUFJLENBQUNVLE1BQU0sSUFBSWI7Z0JBQ25CQSxhQUFhO1lBQ2Y7WUFDQUEsYUFBYTtZQUViLG9CQUFvQjtZQUNwQixJQUFJekksV0FBVztvQkFlV0Esc0JBQ0pBLHVCQUNHQSx1QkFDRkEsdUJBU2pCQSx1QkF5QmlCQSxzQkFDSUEsdUJBQ0lBLHVCQUNOQSx1QkFDREEsdUJBa0JIQSw2QkFDQUEsOEJBQ0NBLDhCQUNHQSw4QkFVbkJBO2dCQXRGSm9JLElBQUlNLFdBQVcsQ0FBQztnQkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhO2dCQUN6QlAsSUFBSVEsSUFBSSxDQUFDLHlCQUF5QixJQUFJSDtnQkFDdENBLGFBQWE7Z0JBRWIsa0JBQWtCO2dCQUNsQkwsSUFBSU0sV0FBVyxDQUFDO2dCQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7Z0JBQ3pCUCxJQUFJUSxJQUFJLENBQUMsbUJBQW1CLElBQUlIO2dCQUNoQ0EsYUFBYTtnQkFFYkwsSUFBSU0sV0FBVyxDQUFDO2dCQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7Z0JBQ3pCLE1BQU1qQyxZQUFZO29CQUNmLHNCQUE4RCxPQUF6QzFHLEVBQUFBLHVCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDJDQUFBQSxxQkFBcUIyRyxjQUFjLEtBQUk7b0JBQzVELGtCQUFvRCxPQUFuQzNHLEVBQUFBLHdCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDRDQUFBQSxzQkFBcUI0RyxRQUFRLEtBQUksR0FBRTtvQkFDcEQscUJBQThELE9BQTFDNUcsRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQmdILGVBQWUsS0FBSTtvQkFDNUQsbUJBQTBFLE9BQXhEaEgsRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQitHLFlBQVksS0FBSTtpQkFDekQ7Z0JBRURMLFVBQVU3RSxPQUFPLENBQUMwSCxDQUFBQTtvQkFDaEJuQixJQUFJUSxJQUFJLENBQUNXLE1BQU0sSUFBSWQ7b0JBQ25CQSxhQUFhO2dCQUNmO2dCQUVBLDRDQUE0QztnQkFDNUMsSUFBSXpJLEVBQUFBLHdCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDRDQUFBQSxzQkFBcUI4RyxhQUFhLEtBQUlwRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFM0IsTUFBTSxHQUFHLEdBQUc7b0JBQ25Hc0QsYUFBYTtvQkFDYkwsSUFBSU0sV0FBVyxDQUFDO29CQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7b0JBQ3pCUCxJQUFJUSxJQUFJLENBQUMsNkJBQTZCLElBQUlIO29CQUMxQ0EsYUFBYTtvQkFFYkwsSUFBSU0sV0FBVyxDQUFDO29CQUNoQk4sSUFBSU8sT0FBTyxDQUFDLGFBQWE7b0JBQ3pCakcsT0FBT3VCLE9BQU8sQ0FBQ2pFLFVBQVUwRyxTQUFTLENBQUNJLGFBQWEsRUFBRWpGLE9BQU8sQ0FBQzs0QkFBQyxDQUFDMkgsWUFBWTVELE1BQU07d0JBQzVFd0MsSUFBSVEsSUFBSSxDQUFDLEdBQXlDbkUsT0FBdENyQyxzQkFBc0JvSCxhQUFZLE1BQXdDLE9BQXBDL0UsS0FBS29CLEtBQUssQ0FBQyxRQUFvQixNQUFLLE1BQUksSUFBSTRDO3dCQUM5RkEsYUFBYTtvQkFDZjtnQkFDRjtnQkFDQUEsYUFBYTtnQkFFYixrQkFBa0I7Z0JBQ2xCTCxJQUFJTSxXQUFXLENBQUM7Z0JBQ2hCTixJQUFJTyxPQUFPLENBQUMsYUFBYTtnQkFDekJQLElBQUlRLElBQUksQ0FBQyxtQkFBbUIsSUFBSUg7Z0JBQ2hDQSxhQUFhO2dCQUViTCxJQUFJTSxXQUFXLENBQUM7Z0JBQ2hCTixJQUFJTyxPQUFPLENBQUMsYUFBYTtnQkFDekIsTUFBTTFCLFlBQVk7b0JBQ2YsbUJBQTBELE9BQXhDakgsRUFBQUEsdUJBQUFBLFVBQVVpSCxTQUFTLGNBQW5CakgsMkNBQUFBLHFCQUFxQmtILGFBQWEsS0FBSTtvQkFDeEQsdUJBQTRELE9BQXRDbEgsRUFBQUEsd0JBQUFBLFVBQVVpSCxTQUFTLGNBQW5CakgsNENBQUFBLHNCQUFxQm1ILFdBQVcsS0FBSTtvQkFDMUQsMkJBQTJKLE9BQWpJbkgsRUFBQUEsd0JBQUFBLFVBQVVpSCxTQUFTLGNBQW5CakgsNENBQUFBLHNCQUFxQm9ILG9CQUFvQixJQUFHaEYsc0JBQXNCcEMsVUFBVWlILFNBQVMsQ0FBQ0csb0JBQW9CLElBQUk7b0JBQ3hJLHFCQUFzRSxPQUFsRHBILEVBQUFBLHdCQUFBQSxVQUFVaUgsU0FBUyxjQUFuQmpILDRDQUFBQSxzQkFBcUJxSCxjQUFjLEtBQUk7b0JBQzNELG9CQUFvRCxPQUFqQ3JILEVBQUFBLHdCQUFBQSxVQUFVaUgsU0FBUyxjQUFuQmpILDRDQUFBQSxzQkFBcUJ1SCxNQUFNLEtBQUksR0FBRTtpQkFDdEQ7Z0JBRUROLFVBQVVwRixPQUFPLENBQUMwSCxDQUFBQTtvQkFDaEJuQixJQUFJUSxJQUFJLENBQUNXLE1BQU0sSUFBSWQ7b0JBQ25CQSxhQUFhO2dCQUNmO2dCQUNBQSxhQUFhO2dCQUViLG9CQUFvQjtnQkFDcEJMLElBQUlNLFdBQVcsQ0FBQztnQkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhO2dCQUN6QlAsSUFBSVEsSUFBSSxDQUFDLHdCQUF3QixJQUFJSDtnQkFDckNBLGFBQWE7Z0JBRWJMLElBQUlNLFdBQVcsQ0FBQztnQkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhO2dCQUN6QixNQUFNbkIsbUJBQW1CO29CQUN0QixpQkFBc0gsT0FBdEd4SCxFQUFBQSw4QkFBQUEsVUFBVXdILGdCQUFnQixjQUExQnhILGtEQUFBQSw0QkFBNEJ5SCxXQUFXLElBQUcsSUFBMkMsT0FBdkN6SCxVQUFVd0gsZ0JBQWdCLENBQUNDLFdBQVcsSUFBSztvQkFDekcsaUJBQTZELE9BQTdDekgsRUFBQUEsK0JBQUFBLFVBQVV3SCxnQkFBZ0IsY0FBMUJ4SCxtREFBQUEsNkJBQTRCMEgsV0FBVyxLQUFJO29CQUMzRCxrQkFBMkUsT0FBMUQxSCxFQUFBQSwrQkFBQUEsVUFBVXdILGdCQUFnQixjQUExQnhILG1EQUFBQSw2QkFBNEIySCxZQUFZLEtBQUk7b0JBQzdELHFCQUFxRixPQUFqRTNILEVBQUFBLCtCQUFBQSxVQUFVd0gsZ0JBQWdCLGNBQTFCeEgsbURBQUFBLDZCQUE0QjRILGVBQWUsS0FBSTtpQkFDckU7Z0JBRURKLGlCQUFpQjNGLE9BQU8sQ0FBQzBILENBQUFBO29CQUN2Qm5CLElBQUlRLElBQUksQ0FBQ1csTUFBTSxJQUFJZDtvQkFDbkJBLGFBQWE7Z0JBQ2Y7Z0JBQ0FBLGFBQWE7Z0JBRWIsMENBQTBDO2dCQUMxQyxJQUFJekksRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQjZHLG1CQUFtQixLQUFJbkUsT0FBTzZDLElBQUksQ0FBQ3ZGLFVBQVUwRyxTQUFTLENBQUNHLG1CQUFtQixFQUFFMUIsTUFBTSxHQUFHLEdBQUc7b0JBQy9HLElBQUk7d0JBQ0YsNEJBQTRCO3dCQUM1QmlELElBQUlxQixPQUFPO3dCQUNYLElBQUlDLGlCQUFpQjt3QkFFckIsd0JBQXdCO3dCQUN4QnRCLElBQUlNLFdBQVcsQ0FBQzt3QkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhO3dCQUN6QlAsSUFBSVEsSUFBSSxDQUFDLHNCQUFzQlAsWUFBWSxHQUFHcUIsZ0JBQWdCOzRCQUFFYixPQUFPO3dCQUFTO3dCQUNoRmEsa0JBQWtCO3dCQUVsQiw4Q0FBOEM7d0JBQzlDLE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzt3QkFFakQsc0JBQXNCO3dCQUN0QixNQUFNRSxrQkFBa0JDLFNBQVNDLGFBQWEsQ0FBQzt3QkFDL0MsTUFBTUMsa0JBQWtCRixTQUFTQyxhQUFhLENBQUM7d0JBRS9DLElBQUlGLGlCQUFpQjs0QkFDbkIsTUFBTUksU0FBUyxNQUFNdkssa0RBQVdBLENBQUNtSyxpQkFBaUI7Z0NBQ2hEMUUsaUJBQWlCO2dDQUNqQitFLE9BQU87Z0NBQ1BDLFNBQVM7Z0NBQ1RDLFlBQVk7Z0NBQ1poSCxPQUFPeUcsZ0JBQWdCUSxXQUFXO2dDQUNsQ2hILFFBQVF3RyxnQkFBZ0JTLFlBQVk7Z0NBQ3BDQyxTQUFTOzRCQUNYOzRCQUNBLE1BQU1DLFVBQVVQLE9BQU9RLFNBQVMsQ0FBQyxhQUFhOzRCQUU5Q3RDLElBQUlNLFdBQVcsQ0FBQzs0QkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhOzRCQUN6QlAsSUFBSVEsSUFBSSxDQUFDLDRCQUE0QlAsWUFBWSxHQUFHcUIsZ0JBQWdCO2dDQUFFYixPQUFPOzRCQUFTOzRCQUN0RmEsa0JBQWtCOzRCQUVsQixnREFBZ0Q7NEJBQ2hELE1BQU1pQixhQUFhOzRCQUNuQixNQUFNQyxjQUFjOzRCQUNwQixNQUFNQyxTQUFTLENBQUN4QyxZQUFZc0MsVUFBUyxJQUFLOzRCQUMxQ3ZDLElBQUkwQyxRQUFRLENBQUNMLFNBQVMsT0FBT0ksUUFBUW5CLGdCQUFnQmlCLFlBQVlDOzRCQUNqRWxCLGtCQUFrQmtCLGNBQWM7d0JBQ2xDO3dCQUVBLElBQUlYLGlCQUFpQjs0QkFDbkIsTUFBTUMsU0FBUyxNQUFNdkssa0RBQVdBLENBQUNzSyxpQkFBaUI7Z0NBQ2hEN0UsaUJBQWlCO2dDQUNqQitFLE9BQU87Z0NBQ1BDLFNBQVM7Z0NBQ1RDLFlBQVk7Z0NBQ1poSCxPQUFPNEcsZ0JBQWdCSyxXQUFXO2dDQUNsQ2hILFFBQVEyRyxnQkFBZ0JNLFlBQVk7Z0NBQ3BDQyxTQUFTOzRCQUNYOzRCQUNBLE1BQU1DLFVBQVVQLE9BQU9RLFNBQVMsQ0FBQyxhQUFhOzRCQUU5Q3RDLElBQUlNLFdBQVcsQ0FBQzs0QkFDaEJOLElBQUlPLE9BQU8sQ0FBQyxhQUFhOzRCQUN6QlAsSUFBSVEsSUFBSSxDQUFDLHFDQUFxQ1AsWUFBWSxHQUFHcUIsZ0JBQWdCO2dDQUFFYixPQUFPOzRCQUFTOzRCQUMvRmEsa0JBQWtCOzRCQUVsQixnREFBZ0Q7NEJBQ2hELE1BQU1pQixhQUFhOzRCQUNuQixNQUFNQyxjQUFjOzRCQUNwQixNQUFNQyxTQUFTLENBQUN4QyxZQUFZc0MsVUFBUyxJQUFLOzRCQUMxQ3ZDLElBQUkwQyxRQUFRLENBQUNMLFNBQVMsT0FBT0ksUUFBUW5CLGdCQUFnQmlCLFlBQVlDO3dCQUNuRTtvQkFDRixFQUFFLE9BQU9HLFlBQVk7d0JBQ25COUssUUFBUThCLEtBQUssQ0FBQywrQkFBK0JnSjt3QkFDN0MsNkJBQTZCO3dCQUM3QjNDLElBQUlNLFdBQVcsQ0FBQzt3QkFDaEJOLElBQUlRLElBQUksQ0FBQyx5RUFBeUUsSUFBSTtvQkFDeEY7Z0JBQ0Y7WUFDRjtZQUVBLGVBQWU7WUFDZixNQUFNb0MsV0FBVyxrQkFBMEYsT0FBeEU3QyxtQkFBbUI3RixJQUFJLEtBQUl2QyxvQkFBQUEsOEJBQUFBLFFBQVNpSixLQUFLLENBQUNpQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSSxXQUFVLEtBQTBDLE9BQXZDLElBQUluQyxPQUFPb0MsV0FBVyxHQUFHRCxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBQztZQUNsSjdDLElBQUkrQyxJQUFJLENBQUNIO1lBRVQ1SyxNQUFNO2dCQUNKb0csT0FBTztnQkFDUEMsYUFBYTtZQUNmO1FBRUYsRUFBRSxPQUFPMUUsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1xSixpQkFBaUI7UUFDckIsSUFBSSxFQUFDckwsb0JBQUFBLDhCQUFBQSxRQUFTSSxHQUFHLEdBQUU7UUFFbkJRLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0YsTUFBTXNIO1FBQ1IsRUFBRSxPQUFPbEcsT0FBTztZQUNkOUIsUUFBUThCLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDM0IsTUFBTTtnQkFDSm9HLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtRQUNGLFNBQVU7WUFDUjlGLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsMENBQTBDO0lBQzFDLE1BQU0wSyxxQkFBcUIsQ0FBQy9LLGFBQTBCTjtZQXVCbkNBLHNCQUNKQSx1QkFDR0EsdUJBQ0ZBLHVCQUloQkEsdUJBT3FCQSxzQkFDREEsdUJBQ0lBLHVCQUNOQSx1QkFDQUEsdUJBQ0RBLHVCQUlIQSw2QkFDQUEsOEJBQ0NBLDhCQUNHQSw4QkFJbEJBO1FBcERFLE1BQU1zTCxhQUFhLElBQUl4QyxPQUFPQyxrQkFBa0I7UUFDaEQsTUFBTXdDLGNBQWNqTCxZQUFZZ0MsSUFBSSxJQUFJO1FBRXhDLE9BQU8sOENBTUhpSixPQUpRRCxZQUFXLHVEQUtsQmhMLE9BRERpTCxhQUFZLGFBRVhqTCxPQURBQSxZQUFZMEksS0FBSyxFQUFDLGFBRWxCMUksT0FEQUEsWUFBWTJJLEtBQUssSUFBSSxnQkFBZSxhQUVuQzNJLE9BRERBLFlBQVk0SSxLQUFLLElBQUksaUJBQWdCLGNBRXpCLE9BRFg1SSxZQUFZNkksVUFBVSxJQUFJLGlCQUFnQix5QkFFeEM3SSxPQURTLElBQUl3SSxLQUFLeEksWUFBWThJLFNBQVMsRUFBRUwsa0JBQWtCLElBQUcsZ0JBS3hFL0ksT0FKVU0sWUFBWW9CLFFBQVEsSUFBSXBCLFlBQVlvQixRQUFRLENBQUN5RCxNQUFNLEdBQUcsSUFBSTdFLFlBQVlvQixRQUFRLENBQUMySCxJQUFJLENBQUMsUUFBUSxrQkFBaUIscURBeUN0RixPQXJDakNySixZQUFZLHlEQUlHQSxPQURJQSxFQUFBQSx1QkFBQUEsVUFBVTBHLFNBQVMsY0FBbkIxRywyQ0FBQUEscUJBQXFCMkcsY0FBYyxLQUFJLEdBQUUscUJBRTFDM0csT0FESEEsRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQjRHLFFBQVEsS0FBSSxHQUFFLHlCQUVsQzVHLE9BREVBLEVBQUFBLHdCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDRDQUFBQSxzQkFBcUJnSCxlQUFlLEtBQUksR0FBRSxzQkFLNURoSCxPQUpnQkEsRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQitHLFlBQVksS0FBSSxvQkFBbUIsd0NBV25EL0csT0FQckJBLEVBQUFBLHdCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDRDQUFBQSxzQkFBcUI4RyxhQUFhLEtBQUlwRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFM0IsTUFBTSxHQUFHLElBQzlGekMsT0FBT3VCLE9BQU8sQ0FBQ2pFLFVBQVUwRyxTQUFTLENBQUNJLGFBQWEsRUFBRTVDLEdBQUcsQ0FBQztnQkFBQyxDQUFDcEMsU0FBUzhELE1BQU07bUJBQ3JFLEdBQWVuQixPQUFaM0MsU0FBUSxNQUF3QyxPQUFwQzJDLEtBQUtvQixLQUFLLENBQUMsUUFBb0IsTUFBSztXQUNuRHdELElBQUksQ0FBQyxRQUFRLDRCQUEyQiw4REFLdEJySixPQURDQSxFQUFBQSx1QkFBQUEsVUFBVWlILFNBQVMsY0FBbkJqSCwyQ0FBQUEscUJBQXFCa0gsYUFBYSxLQUFJLEdBQUUsMEJBRXJDbEgsT0FESkEsRUFBQUEsd0JBQUFBLFVBQVVpSCxTQUFTLGNBQW5CakgsNENBQUFBLHNCQUFxQm1ILFdBQVcsS0FBSSxHQUFFLDhCQUV4Q25ILE9BRE1BLEVBQUFBLHdCQUFBQSxVQUFVaUgsU0FBUyxjQUFuQmpILDRDQUFBQSxzQkFBcUJvSCxvQkFBb0IsS0FBSSxvQkFBbUIsd0JBRXRFcEgsT0FEQUEsRUFBQUEsd0JBQUFBLFVBQVVpSCxTQUFTLGNBQW5CakgsNENBQUFBLHNCQUFxQnFILGNBQWMsS0FBSSxZQUFXLHdCQUVuRHJILE9BRENBLEVBQUFBLHdCQUFBQSxVQUFVaUgsU0FBUyxjQUFuQmpILDRDQUFBQSxzQkFBcUJzSCxtQkFBbUIsS0FBSSxlQUFjLHVCQUs5RHRILE9BSkdBLEVBQUFBLHdCQUFBQSxVQUFVaUgsU0FBUyxjQUFuQmpILDRDQUFBQSxzQkFBcUJ1SCxNQUFNLEtBQUksR0FBRSxnRUFLcEN2SCxPQURBQSxFQUFBQSw4QkFBQUEsVUFBVXdILGdCQUFnQixjQUExQnhILGtEQUFBQSw0QkFBNEJ5SCxXQUFXLElBQUcsSUFBMkMsT0FBdkN6SCxVQUFVd0gsZ0JBQWdCLENBQUNDLFdBQVcsSUFBSyxjQUFhLG9CQUVyR3pILE9BRERBLEVBQUFBLCtCQUFBQSxVQUFVd0gsZ0JBQWdCLGNBQTFCeEgsbURBQUFBLDZCQUE0QjBILFdBQVcsS0FBSSxHQUFFLHFCQUV6QzFILE9BREhBLEVBQUFBLCtCQUFBQSxVQUFVd0gsZ0JBQWdCLGNBQTFCeEgsbURBQUFBLDZCQUE0QjJILFlBQVksS0FBSSxlQUFjLHdCQUt6RTNILE9BSmtCQSxFQUFBQSwrQkFBQUEsVUFBVXdILGdCQUFnQixjQUExQnhILG1EQUFBQSw2QkFBNEI0SCxlQUFlLEtBQUksbUJBQWtCLHlDQU8vQyxPQUhwQzVILEVBQUFBLDZCQUFBQSxVQUFVNkgsZUFBZSxjQUF6QjdILGlEQUFBQSwyQkFBMkI4SCxhQUFhLEtBQUk5SCxVQUFVNkgsZUFBZSxDQUFDQyxhQUFhLENBQUMzQyxNQUFNLEdBQUcsSUFDN0ZuRixVQUFVNkgsZUFBZSxDQUFDQyxhQUFhLENBQUMwRCxLQUFLLENBQUMsQ0FBQyxHQUFHdEgsR0FBRyxDQUFDdUgsQ0FBQUEsV0FDcEQsR0FBb0RBLE9BQWpELElBQUkzQyxLQUFLMkMsU0FBU0MsSUFBSSxFQUFFM0Msa0JBQWtCLElBQUcsTUFBaUMwQyxPQUE3QkEsU0FBU0UsT0FBTyxFQUFDLGNBQWlERixPQUFyQ0EsU0FBU0csU0FBUyxFQUFDLG9CQUE0RyxPQUExRkgsU0FBUy9KLFFBQVEsQ0FBQ3dDLEdBQUcsQ0FBQzdCLENBQUFBLFlBQWFELHNCQUFzQkMsWUFBWWdILElBQUksQ0FBQyxTQUFTLFNBQ3pNQSxJQUFJLENBQUMsUUFBUSxzQkFBcUIsUUFDbEMsZ0NBQStCLDREQUc3QndDLElBQUk7SUFDUjtJQUVBLElBQUksQ0FBQ2hNLFVBQVUsQ0FBQ0UsU0FBUyxPQUFPO0lBRWhDLGtEQUFrRDtJQUNsRCxNQUFNb0kscUJBQXFCN0gsZUFBZTtRQUN4Q3dMLFFBQVEvTCxRQUFRSSxHQUFHO1FBQ25CbUMsTUFBTXZDLFFBQVF1QyxJQUFJLElBQUksV0FBdUMsT0FBNUJ2QyxRQUFRaUosS0FBSyxDQUFDaUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQzVEaEMsT0FBT2xKLFFBQVFrSixLQUFLLElBQUk7UUFDeEJDLE9BQU9uSixRQUFRbUosS0FBSyxJQUFJO1FBQ3hCQyxZQUFZcEosUUFBUW9KLFVBQVUsSUFBSTtRQUNsQ0gsT0FBT2pKLFFBQVFpSixLQUFLO1FBQ3BCSSxXQUFXckosUUFBUXFKLFNBQVMsSUFBSXJKLFFBQVFnTSxTQUFTLElBQUksSUFBSWpELE9BQU9vQyxXQUFXO1FBQzNFeEosVUFBVTNCLFFBQVEyQixRQUFRLElBQUksRUFBRTtRQUNoQ3NLLGNBQWNqTSxRQUFRa00sTUFBTSxJQUFJbE0sUUFBUWlNLFlBQVksSUFBSUU7SUFDMUQ7SUFJQSxxQkFDRSw4REFBQ2pKO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2pFLDJIQUFJQTtvQ0FBQ2lFLFdBQVU7Ozs7Ozs4Q0FDaEIsOERBQUNpSjtvQ0FBR2pKLFdBQVU7OENBQXNDOzs7Ozs7Ozs7Ozs7c0NBRXRELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM3RCx5REFBTUE7b0NBQ0wrTSxTQUFTcEs7b0NBQ1RxSyxVQUFVN0w7b0NBQ1Y4TCxTQUFRO29DQUNSQyxNQUFLO29DQUNMckosV0FBVTs7c0RBRVYsOERBQUM5RCwySEFBU0E7NENBQUM4RCxXQUFXLGdCQUFnRCxPQUFoQzFDLFlBQVksaUJBQWlCOzs7Ozs7d0NBQVE7Ozs7Ozs7OENBRzdFLDhEQUFDbkIseURBQU1BO29DQUNMK00sU0FBU2hCO29DQUNUaUIsVUFBVTNMO29DQUNWd0MsV0FBVTs7d0NBRVR4Qyw4QkFDQyw4REFBQ3ZCLDJIQUFPQTs0Q0FBQytELFdBQVU7Ozs7O2lFQUVuQiw4REFBQ2xFLDJIQUFRQTs0Q0FBQ2tFLFdBQVU7Ozs7Ozt3Q0FFckJ4QyxnQkFBZ0Isa0JBQWtCOzs7Ozs7OzhDQUVyQyw4REFBQzhMO29DQUFPSixTQUFTdE07b0NBQVNvRCxXQUFVOzhDQUNsQyw0RUFBQ25FLDRIQUFDQTt3Q0FBQ21FLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU1uQiw4REFBQ0Q7b0JBQUl3SixLQUFLekw7b0JBQWlCa0MsV0FBVTs4QkFDbEMxQywwQkFDQyw4REFBQ3lDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQy9ELDJIQUFPQTtnQ0FBQytELFdBQVU7Ozs7OzswQ0FDbkIsOERBQUNtQztnQ0FBS25DLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs2Q0FHdkM7OzRCQUVDbEQsMkJBQ0MsOERBQUNpRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDbUM7NENBQUtuQyxXQUFVO3NEQUNiNUMsY0FBYyxxQ0FBcUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05RCw4REFBQzJDO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNaaUYsbUJBQW1CNkQsWUFBWSxpQkFDOUIsOERBQUNVO2dEQUNDQyxLQUFLeEUsbUJBQW1CNkQsWUFBWTtnREFDcENZLEtBQUt6RSxtQkFBbUI3RixJQUFJO2dEQUM1QlksV0FBVTtnREFDVjJKLFNBQVMsQ0FBQ0M7b0RBQ1JBLEVBQUVDLGFBQWEsQ0FBQzNKLEtBQUssQ0FBQ0ssT0FBTyxHQUFHO2dEQUNsQzs7Ozs7cUVBR0YsOERBQUN4RSwySEFBSUE7Z0RBQUNpRSxXQUFVOzs7Ozs7Ozs7OztzREFHcEIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUMrSjtvRUFBRTlKLFdBQVU7OEVBQXdCOzs7Ozs7OEVBQ3JDLDhEQUFDOEo7b0VBQUU5SixXQUFVOzhFQUEwQmlGLG1CQUFtQjdGLElBQUk7Ozs7Ozs7Ozs7OztzRUFFaEUsOERBQUNXOzs4RUFDQyw4REFBQytKO29FQUFFOUosV0FBVTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUM4SjtvRUFBRTlKLFdBQVU7OEVBQTBCaUYsbUJBQW1CYyxLQUFLOzs7Ozs7Ozs7Ozs7c0VBRWpFLDhEQUFDaEc7OzhFQUNDLDhEQUFDK0o7b0VBQUU5SixXQUFVOzhFQUF3Qjs7Ozs7OzhFQUNyQyw4REFBQzhKO29FQUFFOUosV0FBVTs4RUFBMEJpRixtQkFBbUJlLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHbkUsOERBQUNqRztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQytKO29FQUFFOUosV0FBVTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUM4SjtvRUFBRTlKLFdBQVU7OEVBQTBCaUYsbUJBQW1CZ0IsVUFBVTs7Ozs7Ozs7Ozs7O3NFQUV0RSw4REFBQ2xHOzs4RUFDQyw4REFBQytKO29FQUFFOUosV0FBVTs4RUFBd0I7Ozs7Ozs4RUFDckMsOERBQUM4SjtvRUFBRTlKLFdBQVU7OEVBQTBCaUYsbUJBQW1CYSxLQUFLOzs7Ozs7Ozs7Ozs7c0VBRWpFLDhEQUFDL0Y7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDs7c0ZBQ0MsOERBQUMrSjs0RUFBRTlKLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBQ3JDLDhEQUFDOEo7NEVBQUU5SixXQUFVO3NGQUEwQixJQUFJNEYsS0FBS1gsbUJBQW1CaUIsU0FBUyxFQUFFTCxrQkFBa0I7Ozs7Ozs7Ozs7Ozs4RUFFbEcsOERBQUM5Rjs7c0ZBQ0MsOERBQUMrSjs0RUFBRTlKLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBQ3JDLDhEQUFDOEo7NEVBQUU5SixXQUFVO3NGQUNaaUYsbUJBQW1CekcsUUFBUSxJQUFJeUcsbUJBQW1CekcsUUFBUSxDQUFDeUQsTUFBTSxHQUFHLElBQ2pFZ0QsbUJBQW1CekcsUUFBUSxDQUFDMkgsSUFBSSxDQUFDLFFBQ2pDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FXaEIsOERBQUNwRztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMrSjt3Q0FBRy9KLFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBRWIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3lDO3dEQUFHekMsV0FBVTtrRUFBb0M7Ozs7OztrRUFDbEQsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBb0M7Ozs7OzswRUFDcEQsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQ2JsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXMEcsU0FBUyxjQUFwQjFHLDJDQUFBQSxxQkFBc0IyRyxjQUFjLEtBQUk7Ozs7Ozs7Ozs7OztrRUFHN0MsOERBQUMxRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3BELDhEQUFDbUM7Z0VBQUtuQyxXQUFVOztvRUFBeUJsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLHdCQUFBQSxVQUFXMEcsU0FBUyxjQUFwQjFHLDRDQUFBQSxzQkFBc0I0RyxRQUFRLEtBQUk7b0VBQUU7Ozs7Ozs7Ozs7Ozs7a0VBRS9FLDhEQUFDM0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUFvQzs7Ozs7OzBFQUNwRCw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBeUJsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLHdCQUFBQSxVQUFXMEcsU0FBUyxjQUFwQjFHLDRDQUFBQSxzQkFBc0JnSCxlQUFlLEtBQUk7Ozs7Ozs7Ozs7OztrRUFFcEYsOERBQUMvRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3BELDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUF5QmxELENBQUFBLHNCQUFBQSxpQ0FBQUEsd0JBQUFBLFVBQVcwRyxTQUFTLGNBQXBCMUcsNENBQUFBLHNCQUFzQitHLFlBQVksS0FBSTs7Ozs7Ozs7Ozs7O29EQUVoRi9HLENBQUFBLHNCQUFBQSxpQ0FBQUEsd0JBQUFBLFVBQVcwRyxTQUFTLGNBQXBCMUcsNENBQUFBLHNCQUFzQjhHLGFBQWEsS0FDbkNwRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFM0IsTUFBTSxHQUFHLEtBQ3hEbkYsVUFBVTBHLFNBQVMsQ0FBQ0MsY0FBYyxHQUFHLG1CQUNwQyw4REFBQzFEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBb0M7Ozs7OzswRUFDcEQsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNaUixPQUFPdUIsT0FBTyxDQUFDakUsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFNUMsR0FBRyxDQUFDO3dFQUFDLENBQUNzRixZQUFZNUQsTUFBTTt5RkFDekUsOERBQUMzQzt3RUFBcUJDLFdBQVU7OzBGQUM5Qiw4REFBQ21DO2dGQUFLbkMsV0FBVTs7b0ZBQTRCZCxzQkFBc0JvSDtvRkFBWTs7Ozs7OzswRkFDOUUsOERBQUNuRTtnRkFBS25DLFdBQVU7O29GQUFpQnVCLEtBQUtvQixLQUFLLENBQUMsUUFBb0I7b0ZBQUs7Ozs7Ozs7O3VFQUY3RDJEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFXcEIsOERBQUN2RztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN5Qzt3REFBR3pDLFdBQVU7a0VBQW9DOzs7Ozs7a0VBQ2xELDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3BELDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUF5QmxELENBQUFBLHNCQUFBQSxpQ0FBQUEsdUJBQUFBLFVBQVdpSCxTQUFTLGNBQXBCakgsMkNBQUFBLHFCQUFzQmtILGFBQWEsS0FBSTs7Ozs7Ozs7Ozs7O2tFQUVsRiw4REFBQ2pFO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBb0M7Ozs7OzswRUFDcEQsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQXlCbEQsQ0FBQUEsc0JBQUFBLGlDQUFBQSx3QkFBQUEsVUFBV2lILFNBQVMsY0FBcEJqSCw0Q0FBQUEsc0JBQXNCbUgsV0FBVyxLQUFJOzs7Ozs7Ozs7Ozs7a0VBRWhGLDhEQUFDbEU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUFvQzs7Ozs7OzBFQUNwRCw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFDYmxELENBQUFBLHNCQUFBQSxpQ0FBQUEsd0JBQUFBLFVBQVdpSCxTQUFTLGNBQXBCakgsNENBQUFBLHNCQUFzQm9ILG9CQUFvQixJQUN6Q2hGLHNCQUFzQnBDLFVBQVVpSCxTQUFTLENBQUNHLG9CQUFvQixJQUFJOzs7Ozs7Ozs7Ozs7a0VBR3hFLDhEQUFDbkU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUFvQzs7Ozs7OzBFQUNwRCw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBeUJsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLHdCQUFBQSxVQUFXaUgsU0FBUyxjQUFwQmpILDRDQUFBQSxzQkFBc0JxSCxjQUFjLEtBQUk7Ozs7Ozs7Ozs7OztrRUFFbkYsOERBQUNwRTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3BELDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUF5QmxELENBQUFBLHNCQUFBQSxpQ0FBQUEsd0JBQUFBLFVBQVdpSCxTQUFTLGNBQXBCakgsNENBQUFBLHNCQUFzQnNILG1CQUFtQixLQUFJOzs7Ozs7Ozs7Ozs7a0VBRXhGLDhEQUFDckU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUFvQzs7Ozs7OzBFQUNwRCw4REFBQ21DO2dFQUFLbkMsV0FBVTs7b0VBQXlCbEQsQ0FBQUEsc0JBQUFBLGlDQUFBQSx3QkFBQUEsVUFBV2lILFNBQVMsY0FBcEJqSCw0Q0FBQUEsc0JBQXNCdUgsTUFBTSxLQUFJO29FQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUsvRSw4REFBQ3RFO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3lDO3dEQUFHekMsV0FBVTtrRUFBb0M7Ozs7OztrRUFDbEQsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBb0M7Ozs7OzswRUFDcEQsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQ2JsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLDhCQUFBQSxVQUFXd0gsZ0JBQWdCLGNBQTNCeEgsa0RBQUFBLDRCQUE2QnlILFdBQVcsSUFBRyxJQUEyQyxPQUF2Q3pILFVBQVV3SCxnQkFBZ0IsQ0FBQ0MsV0FBVyxJQUFLOzs7Ozs7Ozs7Ozs7a0VBRy9GLDhEQUFDeEU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUFvQzs7Ozs7OzBFQUNwRCw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBeUJsRCxDQUFBQSxzQkFBQUEsaUNBQUFBLCtCQUFBQSxVQUFXd0gsZ0JBQWdCLGNBQTNCeEgsbURBQUFBLDZCQUE2QjBILFdBQVcsS0FBSTs7Ozs7Ozs7Ozs7O2tFQUV2Riw4REFBQ3pFO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ21DO2dFQUFLbkMsV0FBVTswRUFBb0M7Ozs7OzswRUFDcEQsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQXlCbEQsQ0FBQUEsc0JBQUFBLGlDQUFBQSwrQkFBQUEsVUFBV3dILGdCQUFnQixjQUEzQnhILG1EQUFBQSw2QkFBNkIySCxZQUFZLEtBQUk7Ozs7Ozs7Ozs7OztrRUFFeEYsOERBQUMxRTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQztnRUFBS25DLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3BELDhEQUFDbUM7Z0VBQUtuQyxXQUFVOzBFQUF5QmxELENBQUFBLHNCQUFBQSxpQ0FBQUEsK0JBQUFBLFVBQVd3SCxnQkFBZ0IsY0FBM0J4SCxtREFBQUEsNkJBQTZCNEgsZUFBZSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT2hHNUgsYUFDQyxHQUFDQSx3QkFBQUEsVUFBVTBHLFNBQVMsY0FBbkIxRyw0Q0FBQUEsc0JBQXFCNkcsbUJBQW1CLEtBQUluRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0csbUJBQW1CLEVBQUUxQixNQUFNLEdBQUcsS0FDMUduRixFQUFBQSx3QkFBQUEsVUFBVTBHLFNBQVMsY0FBbkIxRyw0Q0FBQUEsc0JBQXFCOEcsYUFBYSxLQUFJcEUsT0FBTzZDLElBQUksQ0FBQ3ZGLFVBQVUwRyxTQUFTLENBQUNJLGFBQWEsRUFBRTNCLE1BQU0sR0FBRyxDQUFDLG1CQUVoRyw4REFBQ2xDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQytKO3dDQUFHL0osV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNEO3dDQUFJQyxXQUFVOzs0Q0FFWmxELEVBQUFBLHdCQUFBQSxVQUFVMEcsU0FBUyxjQUFuQjFHLDRDQUFBQSxzQkFBcUI2RyxtQkFBbUIsS0FDeENuRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0csbUJBQW1CLEVBQUUxQixNQUFNLEdBQUcsbUJBQzdELDhEQUFDbEM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDeUM7d0RBQUd6QyxXQUFVO2tFQUF5Qzs7Ozs7O2tFQUN2RCw4REFBQ1g7d0RBQXNCQyxNQUFNeEMsVUFBVTBHLFNBQVMsQ0FBQ0csbUJBQW1COzs7Ozs7Ozs7Ozs7NENBS3ZFN0csRUFBQUEsd0JBQUFBLFVBQVUwRyxTQUFTLGNBQW5CMUcsNENBQUFBLHNCQUFxQjhHLGFBQWEsS0FDbENwRSxPQUFPNkMsSUFBSSxDQUFDdkYsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFM0IsTUFBTSxHQUFHLG1CQUN2RCw4REFBQ2xDOztrRUFDQyw4REFBQzBDO3dEQUFHekMsV0FBVTtrRUFBeUM7Ozs7OztrRUFDdkQsOERBQUNvQzt3REFBaUI5QyxNQUFNeEMsVUFBVTBHLFNBQVMsQ0FBQ0ksYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQVFsRTlHLENBQUFBLHNCQUFBQSxpQ0FBQUEsNkJBQUFBLFVBQVc2SCxlQUFlLGNBQTFCN0gsaURBQUFBLDJCQUE0QjhILGFBQWEsS0FBSTlILFVBQVU2SCxlQUFlLENBQUNDLGFBQWEsQ0FBQzNDLE1BQU0sR0FBRyxrQkFDN0YsOERBQUNsQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMrSjt3Q0FBRy9KLFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmxELFVBQVU2SCxlQUFlLENBQUNDLGFBQWEsQ0FBQzBELEtBQUssQ0FBQyxDQUFDLEdBQUd0SCxHQUFHLENBQUMsQ0FBQ3VILFVBQVV0SCxzQkFDaEUsOERBQUNsQjtnREFBZ0JDLFdBQVU7O2tFQUN6Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ1osSUFBSTRGLEtBQUsyQyxTQUFTQyxJQUFJLEVBQUUzQyxrQkFBa0I7Ozs7OztrRUFFN0MsOERBQUM5Rjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEOztvRUFBSTtvRUFBVXdJLFNBQVNFLE9BQU87Ozs7Ozs7MEVBQy9CLDhEQUFDMUk7O29FQUFJO29FQUFPd0ksU0FBU0csU0FBUztvRUFBQzs7Ozs7OzswRUFDL0IsOERBQUMzSTs7b0VBQUk7b0VBQVd3SSxTQUFTL0osUUFBUSxDQUFDd0MsR0FBRyxDQUFDN0IsQ0FBQUEsWUFBYUQsc0JBQXNCQyxZQUFZZ0gsSUFBSSxDQUFDLFNBQVM7Ozs7Ozs7Ozs7Ozs7OytDQVA3RmxGOzs7Ozs7Ozs7Ozs7Ozs7dUNBYWQsQ0FBQzNELDJCQUNILDhEQUFDeUM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2hFLDRIQUFhQTs0Q0FBQ2dFLFdBQVU7Ozs7OztzREFDekIsOERBQUMrSjs0Q0FBRy9KLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDOEo7NENBQUU5SixXQUFVO3NEQUFxQjs7Ozs7O3NEQUdsQyw4REFBQzdELHlEQUFNQTs0Q0FDTCtNLFNBQVNwSzs0Q0FDVHNLLFNBQVE7NENBQ1JDLE1BQUs7NENBQ0xySixXQUFVOzs4REFFViw4REFBQzlELDJIQUFTQTtvREFBQzhELFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVl4RDtHQTczQndCdEQ7O1FBRUpOLHNEQUFRQTs7O0tBRkpNIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcYWRtaW5cXHN0dWRlbnQtZGV0YWlscy1tb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB7IFgsIERvd25sb2FkLCBVc2VyLCBNZXNzYWdlQ2lyY2xlLCBMb2FkZXIyLCBSZWZyZXNoQ3cgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCBqc1BERiBmcm9tICdqc3BkZidcclxuaW1wb3J0IGh0bWwyY2FudmFzIGZyb20gJ2h0bWwyY2FudmFzJ1xyXG5cclxuXHJcbmludGVyZmFjZSBTdHVkZW50IHtcclxuICBfaWQ6IHN0cmluZ1xyXG4gIGVtYWlsOiBzdHJpbmdcclxuICBwYXNzd29yZDogc3RyaW5nXHJcbiAgbmFtZT86IHN0cmluZ1xyXG4gIHBob25lPzogc3RyaW5nXHJcbiAgY2xhc3M/OiBzdHJpbmdcclxuICBzY2hvb2xOYW1lPzogc3RyaW5nXHJcbiAgcGxhY2U/OiBzdHJpbmdcclxuICBjcmVhdGVkT24/OiBzdHJpbmdcclxuICBjcmVhdGVkQXQ/OiBzdHJpbmdcclxuICBjcmVhdGVkQnk/OiBzdHJpbmdcclxuICBhdmF0YXI/OiBzdHJpbmdcclxuICBwcm9maWxlSW1hZ2U/OiBzdHJpbmdcclxuICBzdWJqZWN0cz86IHN0cmluZ1tdXHJcbiAgcm9sZT86IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgU3R1ZGVudEluZm8ge1xyXG4gIHVzZXJJZDogc3RyaW5nXHJcbiAgZW1haWw6IHN0cmluZ1xyXG4gIG5hbWU6IHN0cmluZ1xyXG4gIHBob25lOiBzdHJpbmdcclxuICBjbGFzczogc3RyaW5nXHJcbiAgc2Nob29sTmFtZTogc3RyaW5nXHJcbiAgcHJvZmlsZUltYWdlPzogc3RyaW5nXHJcbiAgY3JlYXRlZEF0OiBzdHJpbmdcclxuICBzdWJqZWN0czogc3RyaW5nW11cclxufVxyXG5cclxuXHJcblxyXG5pbnRlcmZhY2UgU3R1ZGVudEFuYWx5dGljcyB7XHJcbiAgcXVpelN0YXRzOiB7XHJcbiAgICB0b3RhbEF0dGVtcHRlZDogbnVtYmVyXHJcbiAgICBhY2N1cmFjeTogbnVtYmVyXHJcbiAgICBzdWJqZWN0V2lzZUF0dGVtcHRzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+XHJcbiAgICBhdmVyYWdlU2NvcmVzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+XHJcbiAgICBsYXN0UXVpekRhdGU6IHN0cmluZ1xyXG4gICAgdG9waWNzQ29tcGxldGVkOiBudW1iZXJcclxuICB9XHJcbiAgY2hhdFN0YXRzOiB7XHJcbiAgICB0b3RhbE1lc3NhZ2VzOiBudW1iZXJcclxuICAgIHRvdGFsRG91YnRzOiBudW1iZXJcclxuICAgIG1vc3REaXNjdXNzZWRTdWJqZWN0OiBzdHJpbmdcclxuICAgIHRvdGFsVGltZVNwZW50OiBzdHJpbmdcclxuICAgIHRpbWVPZkRheU1vc3RBY3RpdmU6IHN0cmluZ1xyXG4gICAgc3RyZWFrOiBudW1iZXJcclxuICB9XHJcbiAgbGVhZGVyYm9hcmRTdGF0czoge1xyXG4gICAgY3VycmVudFJhbms6IG51bWJlclxyXG4gICAgc3BhcmtQb2ludHM6IG51bWJlclxyXG4gICAgcmFua01vdmVtZW50OiBzdHJpbmdcclxuICAgIG1vdGl2YXRpb25MZXZlbDogc3RyaW5nXHJcbiAgfVxyXG4gIGFjdGl2aXR5UGF0dGVybjoge1xyXG4gICAgZGFpbHlBY3Rpdml0eTogQXJyYXk8e1xyXG4gICAgICBkYXRlOiBzdHJpbmdcclxuICAgICAgcXVlcmllczogbnVtYmVyXHJcbiAgICAgIHRpbWVTcGVudDogbnVtYmVyXHJcbiAgICAgIHN1YmplY3RzOiBzdHJpbmdbXVxyXG4gICAgfT5cclxuICAgIHdlZWtseVBhdHRlcm46IFJlY29yZDxzdHJpbmcsIG51bWJlcj5cclxuICAgIG1vbnRobHlUcmVuZDogQXJyYXk8e1xyXG4gICAgICBtb250aDogc3RyaW5nXHJcbiAgICAgIGFjdGl2aXR5OiBudW1iZXJcclxuICAgIH0+XHJcbiAgfVxyXG59XHJcblxyXG5pbnRlcmZhY2UgU3ViamVjdCB7XHJcbiAgX2lkOiBzdHJpbmdcclxuICBuYW1lOiBzdHJpbmdcclxuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgU3R1ZGVudERldGFpbHNNb2RhbFByb3BzIHtcclxuICBpc09wZW46IGJvb2xlYW5cclxuICBvbkNsb3NlOiAoKSA9PiB2b2lkXHJcbiAgc3R1ZGVudDogU3R1ZGVudCB8IG51bGxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3R1ZGVudERldGFpbHNNb2RhbCh7IGlzT3Blbiwgb25DbG9zZSwgc3R1ZGVudCB9OiBTdHVkZW50RGV0YWlsc01vZGFsUHJvcHMpIHtcclxuICBjb25zb2xlLmxvZygnU3R1ZGVudERldGFpbHNNb2RhbCByZW5kZXJlZCB3aXRoOicsIHsgaXNPcGVuLCBzdHVkZW50OiBzdHVkZW50Py5faWQgfSlcclxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcbiAgY29uc3QgW2FuYWx5dGljcywgc2V0QW5hbHl0aWNzXSA9IHVzZVN0YXRlPFN0dWRlbnRBbmFseXRpY3MgfCBudWxsPihudWxsKVxyXG5cclxuICBjb25zdCBbc3R1ZGVudEluZm8sIHNldFN0dWRlbnRJbmZvXSA9IHVzZVN0YXRlPFN0dWRlbnRJbmZvIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW2lzRG93bmxvYWRpbmcsIHNldElzRG93bmxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW3N1YmplY3RNYXAsIHNldFN1YmplY3RNYXBdID0gdXNlU3RhdGU8e1trZXk6IHN0cmluZ106IFN1YmplY3R9Pih7fSlcclxuICBjb25zdCBbc3ViamVjdHNMb2FkaW5nLCBzZXRTdWJqZWN0c0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgbW9kYWxDb250ZW50UmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxyXG5cclxuICAvLyBGZXRjaCBzdWJqZWN0cyBmb3IgbWFwcGluZyBJRHMgdG8gbmFtZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hTdWJqZWN0cyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRTdWJqZWN0c0xvYWRpbmcodHJ1ZSlcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCd9L2FkbWluL3N1YmplY3RzYCwge1xyXG4gICAgICAgICAgaGVhZGVyczogZ2V0QXV0aEhlYWRlcnMoKVxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgY29uc3Qgc3ViamVjdHMgPSBhd2FpdCByZXNwb25zZS5qc29uKClcclxuICAgICAgICAgIGNvbnN0IG1hcHBpbmc6IHtba2V5OiBzdHJpbmddOiBTdWJqZWN0fSA9IHt9XHJcbiAgICAgICAgICBzdWJqZWN0cy5mb3JFYWNoKChzdWJqZWN0OiBTdWJqZWN0KSA9PiB7XHJcbiAgICAgICAgICAgIG1hcHBpbmdbc3ViamVjdC5faWRdID0gc3ViamVjdFxyXG4gICAgICAgICAgfSlcclxuICAgICAgICAgIHNldFN1YmplY3RNYXAobWFwcGluZylcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHN1YmplY3RzOicsIGVycm9yKVxyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldFN1YmplY3RzTG9hZGluZyhmYWxzZSlcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChpc09wZW4pIHtcclxuICAgICAgZmV0Y2hTdWJqZWN0cygpXHJcbiAgICB9XHJcbiAgfSwgW2lzT3Blbl0pXHJcblxyXG4gIC8vIEZldGNoIHN0dWRlbnQgYW5hbHl0aWNzIHdoZW4gbW9kYWwgb3BlbnNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzT3BlbiAmJiBzdHVkZW50Py5faWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ01vZGFsIG9wZW5lZCBmb3Igc3R1ZGVudDonLCBzdHVkZW50Ll9pZClcclxuICAgICAgZmV0Y2hTdHVkZW50QW5hbHl0aWNzKClcclxuICAgIH1cclxuICB9LCBbaXNPcGVuLCBzdHVkZW50Py5faWRdKSAvLyBmZXRjaFN0dWRlbnRBbmFseXRpY3MgaXMgc3RhYmxlLCBubyBuZWVkIHRvIGFkZCBhcyBkZXBlbmRlbmN5XHJcblxyXG4gIGNvbnN0IGdldEF1dGhIZWFkZXJzID0gKCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWNjZXNzVG9rZW4nKVxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNvbnZlcnQgc3ViamVjdCBJRCB0byBuYW1lXHJcbiAgY29uc3QgZ2V0U3ViamVjdERpc3BsYXlOYW1lID0gKHN1YmplY3RJZDogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBzdWJqZWN0ID0gc3ViamVjdE1hcFtzdWJqZWN0SWRdXHJcbiAgICByZXR1cm4gc3ViamVjdCA/IHN1YmplY3QubmFtZSA6IHN1YmplY3RJZCAvLyBGYWxsYmFjayB0byBJRCBpZiBub3QgZm91bmRcclxuICB9XHJcblxyXG4gIC8vIFNpbXBsZSBwaWUgY2hhcnQgY29tcG9uZW50IGZvciBxdWl6IGRpc3RyaWJ1dGlvblxyXG4gIGNvbnN0IFF1aXpEaXN0cmlidXRpb25DaGFydCA9ICh7IGRhdGEgfTogeyBkYXRhOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+IH0pID0+IHtcclxuICAgIGNvbnN0IHRvdGFsID0gT2JqZWN0LnZhbHVlcyhkYXRhKS5yZWR1Y2UoKHN1bSwgdmFsKSA9PiBzdW0gKyB2YWwsIDApXHJcbiAgICBpZiAodG90YWwgPT09IDApIHJldHVybiBudWxsXHJcblxyXG4gICAgY29uc3QgY29sb3JzID0gWycjM0I4MkY2JywgJyNFRjQ0NDQnLCAnIzEwQjk4MScsICcjRjU5RTBCJywgJyM4QjVDRjYnXVxyXG4gICAgbGV0IGN1cnJlbnRBbmdsZSA9IDBcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyXCIgZGF0YS1jaGFydD1cInBpZVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItM1wiIHN0eWxlPXt7IHdpZHRoOiAnMTUwcHgnLCBoZWlnaHQ6ICcxNTBweCcgfX0+XHJcbiAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgIHdpZHRoPVwiMTUwXCJcclxuICAgICAgICAgICAgaGVpZ2h0PVwiMTUwXCJcclxuICAgICAgICAgICAgdmlld0JveD1cIjAgMCAxNTAgMTUwXCJcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidHJhbnNmb3JtIC1yb3RhdGUtOTBcIlxyXG4gICAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIGNpcmNsZSBmb3IgYmV0dGVyIHZpc2liaWxpdHkgKi99XHJcbiAgICAgICAgICAgIDxjaXJjbGVcclxuICAgICAgICAgICAgICBjeD1cIjc1XCJcclxuICAgICAgICAgICAgICBjeT1cIjc1XCJcclxuICAgICAgICAgICAgICByPVwiNjBcIlxyXG4gICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICAgICAgICBzdHJva2U9XCIjZTVlN2ViXCJcclxuICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjFcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoZGF0YSkubWFwKChbc3ViamVjdCwgY291bnRdLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSAoY291bnQgLyB0b3RhbCkgKiAxMDBcclxuICAgICAgICAgICAgICBjb25zdCBhbmdsZSA9IChwZXJjZW50YWdlIC8gMTAwKSAqIDM2MFxyXG4gICAgICAgICAgICAgIGNvbnN0IHN0YXJ0QW5nbGUgPSBjdXJyZW50QW5nbGVcclxuICAgICAgICAgICAgICBjdXJyZW50QW5nbGUgKz0gYW5nbGVcclxuXHJcbiAgICAgICAgICAgICAgY29uc3QgeDEgPSA3NSArIDYwICogTWF0aC5jb3MoKHN0YXJ0QW5nbGUgKiBNYXRoLlBJKSAvIDE4MClcclxuICAgICAgICAgICAgICBjb25zdCB5MSA9IDc1ICsgNjAgKiBNYXRoLnNpbigoc3RhcnRBbmdsZSAqIE1hdGguUEkpIC8gMTgwKVxyXG4gICAgICAgICAgICAgIGNvbnN0IHgyID0gNzUgKyA2MCAqIE1hdGguY29zKCgoc3RhcnRBbmdsZSArIGFuZ2xlKSAqIE1hdGguUEkpIC8gMTgwKVxyXG4gICAgICAgICAgICAgIGNvbnN0IHkyID0gNzUgKyA2MCAqIE1hdGguc2luKCgoc3RhcnRBbmdsZSArIGFuZ2xlKSAqIE1hdGguUEkpIC8gMTgwKVxyXG5cclxuICAgICAgICAgICAgICBjb25zdCBsYXJnZUFyY0ZsYWcgPSBhbmdsZSA+IDE4MCA/IDEgOiAwXHJcblxyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICBrZXk9e3N1YmplY3R9XHJcbiAgICAgICAgICAgICAgICAgIGQ9e2BNIDc1IDc1IEwgJHt4MX0gJHt5MX0gQSA2MCA2MCAwICR7bGFyZ2VBcmNGbGFnfSAxICR7eDJ9ICR7eTJ9IFpgfVxyXG4gICAgICAgICAgICAgICAgICBmaWxsPXtjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXX1cclxuICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiI2ZmZmZmZlwiXHJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOm9wYWNpdHktODAgdHJhbnNpdGlvbi1vcGFjaXR5XCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9KX1cclxuICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAganVzdGlmeS1jZW50ZXIgZ2FwLTIgdGV4dC14cyBtYXgtdy1zbVwiPlxyXG4gICAgICAgICAge09iamVjdC5lbnRyaWVzKGRhdGEpLm1hcCgoW3N1YmplY3QsIGNvdW50XSwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgPGRpdiBrZXk9e3N1YmplY3R9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLXNtXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2dldFN1YmplY3REaXNwbGF5TmFtZShzdWJqZWN0KX06IHtjb3VudH08L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgLy8gU2ltcGxlIGJhciBjaGFydCBmb3IgcXVpeiBhY2N1cmFjeVxyXG4gIGNvbnN0IEFjY3VyYWN5QmFyQ2hhcnQgPSAoeyBkYXRhIH06IHsgZGF0YTogUmVjb3JkPHN0cmluZywgbnVtYmVyPiB9KSA9PiB7XHJcbiAgICBpZiAoIWRhdGEgfHwgT2JqZWN0LmtleXMoZGF0YSkubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbFxyXG5cclxuICAgIGNvbnN0IG1heFNjb3JlID0gTWF0aC5tYXgoLi4uT2JqZWN0LnZhbHVlcyhkYXRhKSlcclxuICAgIGNvbnN0IGNvbG9ycyA9IFsnIzNCODJGNicsICcjRUY0NDQ0JywgJyMxMEI5ODEnLCAnI0Y1OUUwQicsICcjOEI1Q0Y2J11cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiBwLTMgYmctd2hpdGUgcm91bmRlZC1sZ1wiIGRhdGEtY2hhcnQ9XCJiYXJcIiBzdHlsZT17eyB3aWR0aDogJzIwMHB4JywgbWluSGVpZ2h0OiAnMTIwcHgnIH19PlxyXG4gICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTIgdGV4dC1zbVwiPlN1YmplY3Qtd2lzZSBBY2N1cmFjeTwvaDQ+XHJcbiAgICAgICAge09iamVjdC5lbnRyaWVzKGRhdGEpLm1hcCgoW3N1YmplY3QsIHNjb3JlXSwgaW5kZXgpID0+IHtcclxuICAgICAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBNYXRoLnJvdW5kKHNjb3JlICogMTAwKVxyXG4gICAgICAgICAgY29uc3QgYmFyV2lkdGggPSBtYXhTY29yZSA+IDAgPyAoc2NvcmUgLyBtYXhTY29yZSkgKiAxMDAgOiAwXHJcbiAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2IGtleT17c3ViamVjdH0gY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+e2dldFN1YmplY3REaXNwbGF5TmFtZShzdWJqZWN0KX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e3BlcmNlbnRhZ2V9JTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC00IHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kIHByLTJcIlxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiBgJHtNYXRoLm1heChiYXJXaWR0aCwgMTApfSVgLCAvLyBNaW5pbXVtIDEwJSB3aWR0aCBmb3IgdmlzaWJpbGl0eVxyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF1cclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXhzIGZvbnQtYm9sZFwiPntwZXJjZW50YWdlfSU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApXHJcbiAgICAgICAgfSl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZmV0Y2hTdHVkZW50QW5hbHl0aWNzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFzdHVkZW50Py5faWQpIHJldHVyblxyXG5cclxuICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBhbmFseXRpY3MgZm9yIHN0dWRlbnQ6Jywgc3R1ZGVudC5faWQpXHJcbiAgICBjb25zb2xlLmxvZygnQVBJIFVSTDonLCBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAnfS9hZG1pbi9hbmFseXRpY3Mvc3R1ZGVudC8ke3N0dWRlbnQuX2lkfWApXHJcblxyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICB0cnkge1xyXG5cclxuICAgICAgLy8gRmlyc3QsIHRyeSB0byBnZXQgdXNlciBkZXRhaWxzIGZyb20gdGhlIGV4aXN0aW5nIHVzZXJzIEFQSVxyXG4gICAgICBjb25zdCB1c2VyUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAnfS91c2Vyc2AsIHtcclxuICAgICAgICBoZWFkZXJzOiBnZXRBdXRoSGVhZGVycygpXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICBsZXQgdXNlckRldGFpbHMgPSBudWxsXHJcbiAgICAgIGlmICh1c2VyUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCB1c2VycyA9IGF3YWl0IHVzZXJSZXNwb25zZS5qc29uKClcclxuICAgICAgICB1c2VyRGV0YWlscyA9IHVzZXJzLmZpbmQoKHU6IHsgX2lkOiBzdHJpbmcgfSkgPT4gdS5faWQgPT09IHN0dWRlbnQuX2lkKVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdGb3VuZCB1c2VyIGRldGFpbHM6JywgdXNlckRldGFpbHMpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFRyeSB0byBmZXRjaCBhbmFseXRpY3MgZnJvbSB0aGUgbmV3IGVuZHBvaW50XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwJ30vYWRtaW4vYW5hbHl0aWNzL3N0dWRlbnQvJHtzdHVkZW50Ll9pZH1gLCB7XHJcbiAgICAgICAgaGVhZGVyczogZ2V0QXV0aEhlYWRlcnMoKVxyXG4gICAgICB9KVxyXG5cclxuICAgICAgY29uc29sZS5sb2coJ0FuYWx5dGljcyBBUEkgcmVzcG9uc2Ugc3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cylcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcclxuICAgICAgICBjb25zb2xlLmxvZygnQW5hbHl0aWNzIGRhdGEgcmVjZWl2ZWQ6JywgZGF0YSlcclxuICAgICAgICBjb25zb2xlLmxvZygnQW5hbHl0aWNzIHN0cnVjdHVyZTonLCBKU09OLnN0cmluZ2lmeShkYXRhLmFuYWx5dGljcywgbnVsbCwgMikpXHJcblxyXG4gICAgICAgIC8vIFNldCBzdHVkZW50IGluZm8gZnJvbSBBUEkgcmVzcG9uc2VcclxuICAgICAgICBpZiAoZGF0YS5zdHVkZW50SW5mbykge1xyXG4gICAgICAgICAgc2V0U3R1ZGVudEluZm8oZGF0YS5zdHVkZW50SW5mbylcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFNldCBhbmFseXRpY3MgZGF0YSBmcm9tIEFQSVxyXG4gICAgICAgIGlmIChkYXRhLmFuYWx5dGljcykge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1NldHRpbmcgYW5hbHl0aWNzIGRhdGE6JywgZGF0YS5hbmFseXRpY3MpXHJcbiAgICAgICAgICBzZXRBbmFseXRpY3MoZGF0YS5hbmFseXRpY3MpXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdObyBhbmFseXRpY3MgZGF0YSByZWNlaXZlZCwgc2hvd2luZyBlbXB0eSBzdGF0ZScpXHJcbiAgICAgICAgICBzZXRBbmFseXRpY3MoY3JlYXRlRmFsbGJhY2tBbmFseXRpY3MoKSlcclxuICAgICAgICB9XHJcblxyXG5cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQW5hbHl0aWNzIEFQSSBub3QgYXZhaWxhYmxlLCBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKVxyXG4gICAgICAgIC8vIFNob3cgZW1wdHkgc3RhdGUgaW5zdGVhZCBvZiBkdW1teSBkYXRhXHJcbiAgICAgICAgc2V0QW5hbHl0aWNzKGNyZWF0ZUZhbGxiYWNrQW5hbHl0aWNzKCkpXHJcblxyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiBcIkluZm9cIixcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkFuYWx5dGljcyBzZXJ2aWNlIGlzIG5vdCBhdmFpbGFibGUuIFNob3dpbmcgZW1wdHkgc3RhdGUuXCIsXHJcbiAgICAgICAgfSlcclxuICAgICAgfVxyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHN0dWRlbnQgYW5hbHl0aWNzOicsIGVycm9yKVxyXG5cclxuICAgICAgLy8gU2hvdyBlbXB0eSBzdGF0ZSBpbnN0ZWFkIG9mIGR1bW15IGRhdGFcclxuICAgICAgc2V0QW5hbHl0aWNzKGNyZWF0ZUZhbGxiYWNrQW5hbHl0aWNzKCkpXHJcblxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gbG9hZCBhbmFseXRpY3MgZGF0YS4gUGxlYXNlIHRyeSByZWZyZXNoaW5nLlwiLFxyXG4gICAgICB9KVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcblxyXG5cclxuICBjb25zdCBjcmVhdGVGYWxsYmFja0FuYWx5dGljcyA9ICgpOiBTdHVkZW50QW5hbHl0aWNzID0+IHtcclxuICAgIC8vIFJldHVybiBlbXB0eSBkYXRhIHdoZW4gbm8gcmVhbCBhbmFseXRpY3MgYXZhaWxhYmxlXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBxdWl6U3RhdHM6IHtcclxuICAgICAgICB0b3RhbEF0dGVtcHRlZDogMCxcclxuICAgICAgICBhY2N1cmFjeTogMCxcclxuICAgICAgICBzdWJqZWN0V2lzZUF0dGVtcHRzOiB7fSxcclxuICAgICAgICBhdmVyYWdlU2NvcmVzOiB7fSxcclxuICAgICAgICBsYXN0UXVpekRhdGU6ICdObyBxdWl6emVzIHRha2VuIHlldCcsXHJcbiAgICAgICAgdG9waWNzQ29tcGxldGVkOiAwXHJcbiAgICAgIH0sXHJcbiAgICAgIGNoYXRTdGF0czoge1xyXG4gICAgICAgIHRvdGFsTWVzc2FnZXM6IDAsXHJcbiAgICAgICAgdG90YWxEb3VidHM6IDAsXHJcbiAgICAgICAgbW9zdERpc2N1c3NlZFN1YmplY3Q6ICdObyBjaGF0IGFjdGl2aXR5IHlldCcsXHJcbiAgICAgICAgdG90YWxUaW1lU3BlbnQ6ICcwaHIgMG1pbicsXHJcbiAgICAgICAgdGltZU9mRGF5TW9zdEFjdGl2ZTogJ05vIGFjdGl2aXR5IHlldCcsXHJcbiAgICAgICAgc3RyZWFrOiAwXHJcbiAgICAgIH0sXHJcbiAgICAgIGxlYWRlcmJvYXJkU3RhdHM6IHtcclxuICAgICAgICBjdXJyZW50UmFuazogMCxcclxuICAgICAgICBzcGFya1BvaW50czogMCxcclxuICAgICAgICByYW5rTW92ZW1lbnQ6ICdObyByYW5rIHlldCcsXHJcbiAgICAgICAgbW90aXZhdGlvbkxldmVsOiAnR2V0dGluZyBzdGFydGVkJ1xyXG4gICAgICB9LFxyXG4gICAgICBhY3Rpdml0eVBhdHRlcm46IHtcclxuICAgICAgICBkYWlseUFjdGl2aXR5OiBbXSxcclxuICAgICAgICB3ZWVrbHlQYXR0ZXJuOiB7fSxcclxuICAgICAgICBtb250aGx5VHJlbmQ6IFtdXHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG5cclxuXHJcbiAgY29uc3QgZ2VuZXJhdGVQREZSZXBvcnQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIW1vZGFsQ29udGVudFJlZi5jdXJyZW50IHx8ICFkaXNwbGF5U3R1ZGVudEluZm8pIHJldHVyblxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENyZWF0ZSBhIG5ldyBQREYgZG9jdW1lbnRcclxuICAgICAgY29uc3QgcGRmID0gbmV3IGpzUERGKCdwJywgJ21tJywgJ2E0JylcclxuICAgICAgY29uc3QgcGFnZVdpZHRoID0gcGRmLmludGVybmFsLnBhZ2VTaXplLmdldFdpZHRoKClcclxuICAgICAgbGV0IHlQb3NpdGlvbiA9IDIwXHJcblxyXG4gICAgICAvLyBBZGQgdGl0bGVcclxuICAgICAgcGRmLnNldEZvbnRTaXplKDIwKVxyXG4gICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKVxyXG4gICAgICBwZGYudGV4dCgnU3R1ZGVudCBBbmFseXRpY3MgUmVwb3J0JywgcGFnZVdpZHRoIC8gMiwgeVBvc2l0aW9uLCB7IGFsaWduOiAnY2VudGVyJyB9KVxyXG4gICAgICB5UG9zaXRpb24gKz0gMTVcclxuXHJcbiAgICAgIC8vIEFkZCBnZW5lcmF0aW9uIGRhdGVcclxuICAgICAgcGRmLnNldEZvbnRTaXplKDEwKVxyXG4gICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ25vcm1hbCcpXHJcbiAgICAgIHBkZi50ZXh0KGBHZW5lcmF0ZWQgb246ICR7bmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKX1gLCBwYWdlV2lkdGggLyAyLCB5UG9zaXRpb24sIHsgYWxpZ246ICdjZW50ZXInIH0pXHJcbiAgICAgIHlQb3NpdGlvbiArPSAyMFxyXG5cclxuICAgICAgLy8gU3R1ZGVudCBJbmZvcm1hdGlvbiBTZWN0aW9uXHJcbiAgICAgIHBkZi5zZXRGb250U2l6ZSgxNClcclxuICAgICAgcGRmLnNldEZvbnQoJ2hlbHZldGljYScsICdib2xkJylcclxuICAgICAgcGRmLnRleHQoJ1N0dWRlbnQgSW5mb3JtYXRpb24nLCAyMCwgeVBvc2l0aW9uKVxyXG4gICAgICB5UG9zaXRpb24gKz0gMTBcclxuXHJcbiAgICAgIHBkZi5zZXRGb250U2l6ZSgxMClcclxuICAgICAgcGRmLnNldEZvbnQoJ2hlbHZldGljYScsICdub3JtYWwnKVxyXG4gICAgICBjb25zdCBzdHVkZW50SW5mbyA9IFtcclxuICAgICAgICBgTmFtZTogJHtkaXNwbGF5U3R1ZGVudEluZm8ubmFtZSB8fCAnVW5rbm93biBTdHVkZW50J31gLFxyXG4gICAgICAgIGBFbWFpbDogJHtkaXNwbGF5U3R1ZGVudEluZm8uZW1haWx9YCxcclxuICAgICAgICBgUGhvbmU6ICR7ZGlzcGxheVN0dWRlbnRJbmZvLnBob25lIHx8ICdOb3QgcHJvdmlkZWQnfWAsXHJcbiAgICAgICAgYENsYXNzOiAke2Rpc3BsYXlTdHVkZW50SW5mby5jbGFzcyB8fCAnTm90IHNwZWNpZmllZCd9YCxcclxuICAgICAgICBgU2Nob29sOiAke2Rpc3BsYXlTdHVkZW50SW5mby5zY2hvb2xOYW1lIHx8ICdOb3Qgc3BlY2lmaWVkJ31gLFxyXG4gICAgICAgIGBSZWdpc3RyYXRpb24gRGF0ZTogJHtuZXcgRGF0ZShkaXNwbGF5U3R1ZGVudEluZm8uY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1gLFxyXG4gICAgICAgIGBTdWJqZWN0czogJHtkaXNwbGF5U3R1ZGVudEluZm8uc3ViamVjdHMgJiYgZGlzcGxheVN0dWRlbnRJbmZvLnN1YmplY3RzLmxlbmd0aCA+IDAgPyBkaXNwbGF5U3R1ZGVudEluZm8uc3ViamVjdHMuam9pbignLCAnKSA6ICdOb25lIHNwZWNpZmllZCd9YFxyXG4gICAgICBdXHJcblxyXG4gICAgICBzdHVkZW50SW5mby5mb3JFYWNoKGluZm8gPT4ge1xyXG4gICAgICAgIHBkZi50ZXh0KGluZm8sIDIwLCB5UG9zaXRpb24pXHJcbiAgICAgICAgeVBvc2l0aW9uICs9IDZcclxuICAgICAgfSlcclxuICAgICAgeVBvc2l0aW9uICs9IDEwXHJcblxyXG4gICAgICAvLyBBbmFseXRpY3MgU2VjdGlvblxyXG4gICAgICBpZiAoYW5hbHl0aWNzKSB7XHJcbiAgICAgICAgcGRmLnNldEZvbnRTaXplKDE0KVxyXG4gICAgICAgIHBkZi5zZXRGb250KCdoZWx2ZXRpY2EnLCAnYm9sZCcpXHJcbiAgICAgICAgcGRmLnRleHQoJ1BlcmZvcm1hbmNlIEFuYWx5dGljcycsIDIwLCB5UG9zaXRpb24pXHJcbiAgICAgICAgeVBvc2l0aW9uICs9IDEwXHJcblxyXG4gICAgICAgIC8vIFF1aXogU3RhdGlzdGljc1xyXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMilcclxuICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKVxyXG4gICAgICAgIHBkZi50ZXh0KCdRdWl6IFN0YXRpc3RpY3MnLCAyMCwgeVBvc2l0aW9uKVxyXG4gICAgICAgIHlQb3NpdGlvbiArPSA4XHJcblxyXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMClcclxuICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ25vcm1hbCcpXHJcbiAgICAgICAgY29uc3QgcXVpelN0YXRzID0gW1xyXG4gICAgICAgICAgYFF1aXp6ZXMgQXR0ZW1wdGVkOiAke2FuYWx5dGljcy5xdWl6U3RhdHM/LnRvdGFsQXR0ZW1wdGVkIHx8IDB9YCxcclxuICAgICAgICAgIGBRdWl6IEFjY3VyYWN5OiAke2FuYWx5dGljcy5xdWl6U3RhdHM/LmFjY3VyYWN5IHx8IDB9JWAsXHJcbiAgICAgICAgICBgVG9waWNzIENvbXBsZXRlZDogJHthbmFseXRpY3MucXVpelN0YXRzPy50b3BpY3NDb21wbGV0ZWQgfHwgMH1gLFxyXG4gICAgICAgICAgYExhc3QgUXVpeiBEYXRlOiAke2FuYWx5dGljcy5xdWl6U3RhdHM/Lmxhc3RRdWl6RGF0ZSB8fCAnTm8gcXVpenplcyB0YWtlbid9YFxyXG4gICAgICAgIF1cclxuXHJcbiAgICAgICAgcXVpelN0YXRzLmZvckVhY2goc3RhdCA9PiB7XHJcbiAgICAgICAgICBwZGYudGV4dChzdGF0LCAyNSwgeVBvc2l0aW9uKVxyXG4gICAgICAgICAgeVBvc2l0aW9uICs9IDZcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICAvLyBBZGQgc3ViamVjdC13aXNlIHBlcmZvcm1hbmNlIGlmIGF2YWlsYWJsZVxyXG4gICAgICAgIGlmIChhbmFseXRpY3MucXVpelN0YXRzPy5hdmVyYWdlU2NvcmVzICYmIE9iamVjdC5rZXlzKGFuYWx5dGljcy5xdWl6U3RhdHMuYXZlcmFnZVNjb3JlcykubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgeVBvc2l0aW9uICs9IDVcclxuICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMSlcclxuICAgICAgICAgIHBkZi5zZXRGb250KCdoZWx2ZXRpY2EnLCAnYm9sZCcpXHJcbiAgICAgICAgICBwZGYudGV4dCgnU3ViamVjdC13aXNlIFBlcmZvcm1hbmNlOicsIDI1LCB5UG9zaXRpb24pXHJcbiAgICAgICAgICB5UG9zaXRpb24gKz0gNlxyXG5cclxuICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMClcclxuICAgICAgICAgIHBkZi5zZXRGb250KCdoZWx2ZXRpY2EnLCAnbm9ybWFsJylcclxuICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGFuYWx5dGljcy5xdWl6U3RhdHMuYXZlcmFnZVNjb3JlcykuZm9yRWFjaCgoW3N1YmplY3RLZXksIHNjb3JlXSkgPT4ge1xyXG4gICAgICAgICAgICBwZGYudGV4dChgJHtnZXRTdWJqZWN0RGlzcGxheU5hbWUoc3ViamVjdEtleSl9OiAke01hdGgucm91bmQoKHNjb3JlIGFzIG51bWJlcikgKiAxMDApfSVgLCAzMCwgeVBvc2l0aW9uKVxyXG4gICAgICAgICAgICB5UG9zaXRpb24gKz0gNVxyXG4gICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICAgICAgeVBvc2l0aW9uICs9IDEwXHJcblxyXG4gICAgICAgIC8vIENoYXQgU3RhdGlzdGljc1xyXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMilcclxuICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKVxyXG4gICAgICAgIHBkZi50ZXh0KCdDaGF0IFN0YXRpc3RpY3MnLCAyMCwgeVBvc2l0aW9uKVxyXG4gICAgICAgIHlQb3NpdGlvbiArPSA4XHJcblxyXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMClcclxuICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ25vcm1hbCcpXHJcbiAgICAgICAgY29uc3QgY2hhdFN0YXRzID0gW1xyXG4gICAgICAgICAgYFRvdGFsIE1lc3NhZ2VzOiAke2FuYWx5dGljcy5jaGF0U3RhdHM/LnRvdGFsTWVzc2FnZXMgfHwgMH1gLFxyXG4gICAgICAgICAgYFRvdGFsIERvdWJ0cyBBc2tlZDogJHthbmFseXRpY3MuY2hhdFN0YXRzPy50b3RhbERvdWJ0cyB8fCAwfWAsXHJcbiAgICAgICAgICBgTW9zdCBEaXNjdXNzZWQgU3ViamVjdDogJHthbmFseXRpY3MuY2hhdFN0YXRzPy5tb3N0RGlzY3Vzc2VkU3ViamVjdCA/IGdldFN1YmplY3REaXNwbGF5TmFtZShhbmFseXRpY3MuY2hhdFN0YXRzLm1vc3REaXNjdXNzZWRTdWJqZWN0KSA6ICdObyBjaGF0IGFjdGl2aXR5J31gLFxyXG4gICAgICAgICAgYFRvdGFsIFRpbWUgU3BlbnQ6ICR7YW5hbHl0aWNzLmNoYXRTdGF0cz8udG90YWxUaW1lU3BlbnQgfHwgJzBociAwbWluJ31gLFxyXG4gICAgICAgICAgYExlYXJuaW5nIFN0cmVhazogJHthbmFseXRpY3MuY2hhdFN0YXRzPy5zdHJlYWsgfHwgMH0gZGF5c2BcclxuICAgICAgICBdXHJcblxyXG4gICAgICAgIGNoYXRTdGF0cy5mb3JFYWNoKHN0YXQgPT4ge1xyXG4gICAgICAgICAgcGRmLnRleHQoc3RhdCwgMjUsIHlQb3NpdGlvbilcclxuICAgICAgICAgIHlQb3NpdGlvbiArPSA2XHJcbiAgICAgICAgfSlcclxuICAgICAgICB5UG9zaXRpb24gKz0gMTBcclxuXHJcbiAgICAgICAgLy8gTGVhZGVyYm9hcmQgU3RhdHNcclxuICAgICAgICBwZGYuc2V0Rm9udFNpemUoMTIpXHJcbiAgICAgICAgcGRmLnNldEZvbnQoJ2hlbHZldGljYScsICdib2xkJylcclxuICAgICAgICBwZGYudGV4dCgnTGVhZGVyYm9hcmQgUG9zaXRpb24nLCAyMCwgeVBvc2l0aW9uKVxyXG4gICAgICAgIHlQb3NpdGlvbiArPSA4XHJcblxyXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMClcclxuICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ25vcm1hbCcpXHJcbiAgICAgICAgY29uc3QgbGVhZGVyYm9hcmRTdGF0cyA9IFtcclxuICAgICAgICAgIGBDdXJyZW50IFJhbms6ICR7YW5hbHl0aWNzLmxlYWRlcmJvYXJkU3RhdHM/LmN1cnJlbnRSYW5rID8gYCMke2FuYWx5dGljcy5sZWFkZXJib2FyZFN0YXRzLmN1cnJlbnRSYW5rfWAgOiAnTm90IHJhbmtlZCd9YCxcclxuICAgICAgICAgIGBTcGFyayBQb2ludHM6ICR7YW5hbHl0aWNzLmxlYWRlcmJvYXJkU3RhdHM/LnNwYXJrUG9pbnRzIHx8IDB9YCxcclxuICAgICAgICAgIGBSYW5rIE1vdmVtZW50OiAke2FuYWx5dGljcy5sZWFkZXJib2FyZFN0YXRzPy5yYW5rTW92ZW1lbnQgfHwgJ05vIG1vdmVtZW50J31gLFxyXG4gICAgICAgICAgYE1vdGl2YXRpb24gTGV2ZWw6ICR7YW5hbHl0aWNzLmxlYWRlcmJvYXJkU3RhdHM/Lm1vdGl2YXRpb25MZXZlbCB8fCAnR2V0dGluZyBzdGFydGVkJ31gXHJcbiAgICAgICAgXVxyXG5cclxuICAgICAgICBsZWFkZXJib2FyZFN0YXRzLmZvckVhY2goc3RhdCA9PiB7XHJcbiAgICAgICAgICBwZGYudGV4dChzdGF0LCAyNSwgeVBvc2l0aW9uKVxyXG4gICAgICAgICAgeVBvc2l0aW9uICs9IDZcclxuICAgICAgICB9KVxyXG4gICAgICAgIHlQb3NpdGlvbiArPSAxNVxyXG5cclxuICAgICAgICAvLyBBZGQgY2hhcnRzIG9uIGEgbmV3IHBhZ2UgaWYgZGF0YSBleGlzdHNcclxuICAgICAgICBpZiAoYW5hbHl0aWNzLnF1aXpTdGF0cz8uc3ViamVjdFdpc2VBdHRlbXB0cyAmJiBPYmplY3Qua2V5cyhhbmFseXRpY3MucXVpelN0YXRzLnN1YmplY3RXaXNlQXR0ZW1wdHMpLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIEFkZCBhIG5ldyBwYWdlIGZvciBjaGFydHNcclxuICAgICAgICAgICAgcGRmLmFkZFBhZ2UoKVxyXG4gICAgICAgICAgICBsZXQgY2hhcnRZUG9zaXRpb24gPSAyMFxyXG5cclxuICAgICAgICAgICAgLy8gQWRkIGNoYXJ0cyBwYWdlIHRpdGxlXHJcbiAgICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxOClcclxuICAgICAgICAgICAgcGRmLnNldEZvbnQoJ2hlbHZldGljYScsICdib2xkJylcclxuICAgICAgICAgICAgcGRmLnRleHQoJ1BlcmZvcm1hbmNlIENoYXJ0cycsIHBhZ2VXaWR0aCAvIDIsIGNoYXJ0WVBvc2l0aW9uLCB7IGFsaWduOiAnY2VudGVyJyB9KVxyXG4gICAgICAgICAgICBjaGFydFlQb3NpdGlvbiArPSAzMFxyXG5cclxuICAgICAgICAgICAgLy8gV2FpdCBhIG1vbWVudCBmb3IgY2hhcnRzIHRvIHJlbmRlciBwcm9wZXJseVxyXG4gICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSlcclxuXHJcbiAgICAgICAgICAgIC8vIEZpbmQgY2hhcnQgZWxlbWVudHNcclxuICAgICAgICAgICAgY29uc3QgcGllQ2hhcnRFbGVtZW50ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignW2RhdGEtY2hhcnQ9XCJwaWVcIl0nKSBhcyBIVE1MRWxlbWVudFxyXG4gICAgICAgICAgICBjb25zdCBiYXJDaGFydEVsZW1lbnQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1jaGFydD1cImJhclwiXScpIGFzIEhUTUxFbGVtZW50XHJcblxyXG4gICAgICAgICAgICBpZiAocGllQ2hhcnRFbGVtZW50KSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgY2FudmFzID0gYXdhaXQgaHRtbDJjYW52YXMocGllQ2hhcnRFbGVtZW50LCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZmZmZmJyxcclxuICAgICAgICAgICAgICAgIHNjYWxlOiAzLFxyXG4gICAgICAgICAgICAgICAgdXNlQ09SUzogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIGFsbG93VGFpbnQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICB3aWR0aDogcGllQ2hhcnRFbGVtZW50Lm9mZnNldFdpZHRoLFxyXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiBwaWVDaGFydEVsZW1lbnQub2Zmc2V0SGVpZ2h0LFxyXG4gICAgICAgICAgICAgICAgbG9nZ2luZzogZmFsc2VcclxuICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgIGNvbnN0IGltZ0RhdGEgPSBjYW52YXMudG9EYXRhVVJMKCdpbWFnZS9wbmcnLCAxLjApXHJcblxyXG4gICAgICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxNClcclxuICAgICAgICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKVxyXG4gICAgICAgICAgICAgIHBkZi50ZXh0KCdRdWl6IEF0dGVtcHRzIGJ5IFN1YmplY3QnLCBwYWdlV2lkdGggLyAyLCBjaGFydFlQb3NpdGlvbiwgeyBhbGlnbjogJ2NlbnRlcicgfSlcclxuICAgICAgICAgICAgICBjaGFydFlQb3NpdGlvbiArPSAxMFxyXG5cclxuICAgICAgICAgICAgICAvLyBBZGQgcGllIGNoYXJ0IC0gc21hbGxlciBhbmQgcHJvcGVybHkgY2VudGVyZWRcclxuICAgICAgICAgICAgICBjb25zdCBjaGFydFdpZHRoID0gNjBcclxuICAgICAgICAgICAgICBjb25zdCBjaGFydEhlaWdodCA9IDUwXHJcbiAgICAgICAgICAgICAgY29uc3QgY2hhcnRYID0gKHBhZ2VXaWR0aCAtIGNoYXJ0V2lkdGgpIC8gMlxyXG4gICAgICAgICAgICAgIHBkZi5hZGRJbWFnZShpbWdEYXRhLCAnUE5HJywgY2hhcnRYLCBjaGFydFlQb3NpdGlvbiwgY2hhcnRXaWR0aCwgY2hhcnRIZWlnaHQpXHJcbiAgICAgICAgICAgICAgY2hhcnRZUG9zaXRpb24gKz0gY2hhcnRIZWlnaHQgKyAxNVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAoYmFyQ2hhcnRFbGVtZW50KSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgY2FudmFzID0gYXdhaXQgaHRtbDJjYW52YXMoYmFyQ2hhcnRFbGVtZW50LCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZmZmZmJyxcclxuICAgICAgICAgICAgICAgIHNjYWxlOiAzLFxyXG4gICAgICAgICAgICAgICAgdXNlQ09SUzogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIGFsbG93VGFpbnQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICB3aWR0aDogYmFyQ2hhcnRFbGVtZW50Lm9mZnNldFdpZHRoLFxyXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiBiYXJDaGFydEVsZW1lbnQub2Zmc2V0SGVpZ2h0LFxyXG4gICAgICAgICAgICAgICAgbG9nZ2luZzogZmFsc2VcclxuICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgIGNvbnN0IGltZ0RhdGEgPSBjYW52YXMudG9EYXRhVVJMKCdpbWFnZS9wbmcnLCAxLjApXHJcblxyXG4gICAgICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxNClcclxuICAgICAgICAgICAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJywgJ2JvbGQnKVxyXG4gICAgICAgICAgICAgIHBkZi50ZXh0KCdTdWJqZWN0LXdpc2UgQWNjdXJhY3kgUGVyZm9ybWFuY2UnLCBwYWdlV2lkdGggLyAyLCBjaGFydFlQb3NpdGlvbiwgeyBhbGlnbjogJ2NlbnRlcicgfSlcclxuICAgICAgICAgICAgICBjaGFydFlQb3NpdGlvbiArPSAxMFxyXG5cclxuICAgICAgICAgICAgICAvLyBBZGQgYmFyIGNoYXJ0IC0gc21hbGxlciBhbmQgcHJvcGVybHkgY2VudGVyZWRcclxuICAgICAgICAgICAgICBjb25zdCBjaGFydFdpZHRoID0gODBcclxuICAgICAgICAgICAgICBjb25zdCBjaGFydEhlaWdodCA9IDYwXHJcbiAgICAgICAgICAgICAgY29uc3QgY2hhcnRYID0gKHBhZ2VXaWR0aCAtIGNoYXJ0V2lkdGgpIC8gMlxyXG4gICAgICAgICAgICAgIHBkZi5hZGRJbWFnZShpbWdEYXRhLCAnUE5HJywgY2hhcnRYLCBjaGFydFlQb3NpdGlvbiwgY2hhcnRXaWR0aCwgY2hhcnRIZWlnaHQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKGNoYXJ0RXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIGNoYXJ0cyB0byBQREY6JywgY2hhcnRFcnJvcilcclxuICAgICAgICAgICAgLy8gQWRkIHRleHQgbm90ZSBhYm91dCBjaGFydHNcclxuICAgICAgICAgICAgcGRmLnNldEZvbnRTaXplKDEyKVxyXG4gICAgICAgICAgICBwZGYudGV4dCgnQ2hhcnRzIGNvdWxkIG5vdCBiZSBnZW5lcmF0ZWQuIFBsZWFzZSB2aWV3IHRoZW0gaW4gdGhlIHdlYiBpbnRlcmZhY2UuJywgMjAsIDUwKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU2F2ZSB0aGUgUERGXHJcbiAgICAgIGNvbnN0IGZpbGVOYW1lID0gYHN0dWRlbnQtcmVwb3J0LSR7ZGlzcGxheVN0dWRlbnRJbmZvLm5hbWUgfHwgc3R1ZGVudD8uZW1haWwuc3BsaXQoJ0AnKVswXSB8fCAndW5rbm93bid9LSR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19LnBkZmBcclxuICAgICAgcGRmLnNhdmUoZmlsZU5hbWUpXHJcblxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRG93bmxvYWQgQ29tcGxldGVcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTdHVkZW50IHJlcG9ydCBoYXMgYmVlbiBkb3dubG9hZGVkIGFzIFBERiB3aXRoIGNoYXJ0cy5cIixcclxuICAgICAgfSlcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIFBERjonLCBlcnJvcilcclxuICAgICAgdGhyb3cgZXJyb3JcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZURvd25sb2FkID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFzdHVkZW50Py5faWQpIHJldHVyblxyXG5cclxuICAgIHNldElzRG93bmxvYWRpbmcodHJ1ZSlcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGdlbmVyYXRlUERGUmVwb3J0KClcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRvd25sb2FkaW5nIHJlcG9ydDonLCBlcnJvcilcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIkVycm9yXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGdlbmVyYXRlIFBERiByZXBvcnQuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0Rvd25sb2FkaW5nKGZhbHNlKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdlbmVyYXRlIHRleHQgcmVwb3J0XHJcbiAgY29uc3QgZ2VuZXJhdGVUZXh0UmVwb3J0ID0gKHN0dWRlbnRJbmZvOiBTdHVkZW50SW5mbywgYW5hbHl0aWNzOiBTdHVkZW50QW5hbHl0aWNzIHwgbnVsbCkgPT4ge1xyXG4gICAgY29uc3QgcmVwb3J0RGF0ZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKClcclxuICAgIGNvbnN0IHN0dWRlbnROYW1lID0gc3R1ZGVudEluZm8ubmFtZSB8fCAnVW5rbm93biBTdHVkZW50J1xyXG5cclxuICAgIHJldHVybiBgXHJcblNUVURZQlVERFkgU1RVREVOVCBSRVBPUlRcclxuR2VuZXJhdGVkIG9uOiAke3JlcG9ydERhdGV9XHJcblxyXG5TVFVERU5UIElORk9STUFUSU9OXHJcbj09PT09PT09PT09PT09PT09PVxyXG5OYW1lOiAke3N0dWRlbnROYW1lfVxyXG5FbWFpbDogJHtzdHVkZW50SW5mby5lbWFpbH1cclxuUGhvbmU6ICR7c3R1ZGVudEluZm8ucGhvbmUgfHwgJ05vdCBwcm92aWRlZCd9XHJcbkNsYXNzOiAke3N0dWRlbnRJbmZvLmNsYXNzIHx8ICdOb3Qgc3BlY2lmaWVkJ31cclxuU2Nob29sOiAke3N0dWRlbnRJbmZvLnNjaG9vbE5hbWUgfHwgJ05vdCBzcGVjaWZpZWQnfVxyXG5SZWdpc3RyYXRpb24gRGF0ZTogJHtuZXcgRGF0ZShzdHVkZW50SW5mby5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxyXG5TdWJqZWN0czogJHtzdHVkZW50SW5mby5zdWJqZWN0cyAmJiBzdHVkZW50SW5mby5zdWJqZWN0cy5sZW5ndGggPiAwID8gc3R1ZGVudEluZm8uc3ViamVjdHMuam9pbignLCAnKSA6ICdOb25lIHNwZWNpZmllZCd9XHJcblxyXG5QRVJGT1JNQU5DRSBBTkFMWVRJQ1NcclxuPT09PT09PT09PT09PT09PT09PT1cclxuJHthbmFseXRpY3MgPyBgXHJcblFVSVogU1RBVElTVElDU1xyXG4tLS0tLS0tLS0tLS0tLVxyXG5RdWl6emVzIEF0dGVtcHRlZDogJHthbmFseXRpY3MucXVpelN0YXRzPy50b3RhbEF0dGVtcHRlZCB8fCAwfVxyXG5RdWl6IEFjY3VyYWN5OiAke2FuYWx5dGljcy5xdWl6U3RhdHM/LmFjY3VyYWN5IHx8IDB9JVxyXG5Ub3BpY3MgQ29tcGxldGVkOiAke2FuYWx5dGljcy5xdWl6U3RhdHM/LnRvcGljc0NvbXBsZXRlZCB8fCAwfVxyXG5MYXN0IFF1aXogRGF0ZTogJHthbmFseXRpY3MucXVpelN0YXRzPy5sYXN0UXVpekRhdGUgfHwgJ05vIHF1aXp6ZXMgdGFrZW4nfVxyXG5cclxuU1VCSkVDVCBTQ09SRVNcclxuLS0tLS0tLS0tLS0tLS1cclxuJHthbmFseXRpY3MucXVpelN0YXRzPy5hdmVyYWdlU2NvcmVzICYmIE9iamVjdC5rZXlzKGFuYWx5dGljcy5xdWl6U3RhdHMuYXZlcmFnZVNjb3JlcykubGVuZ3RoID4gMCA/XHJcbiAgT2JqZWN0LmVudHJpZXMoYW5hbHl0aWNzLnF1aXpTdGF0cy5hdmVyYWdlU2NvcmVzKS5tYXAoKFtzdWJqZWN0LCBzY29yZV0pID0+XHJcbiAgICBgJHtzdWJqZWN0fTogJHtNYXRoLnJvdW5kKChzY29yZSBhcyBudW1iZXIpICogMTAwKX0lYFxyXG4gICkuam9pbignXFxuJykgOiAnTm8gcXVpeiBzY29yZXMgYXZhaWxhYmxlJ31cclxuXHJcbkNIQVQgU1RBVElTVElDU1xyXG4tLS0tLS0tLS0tLS0tLVxyXG5Ub3RhbCBNZXNzYWdlcyBTZW50OiAke2FuYWx5dGljcy5jaGF0U3RhdHM/LnRvdGFsTWVzc2FnZXMgfHwgMH1cclxuVG90YWwgRG91YnRzIEFza2VkOiAke2FuYWx5dGljcy5jaGF0U3RhdHM/LnRvdGFsRG91YnRzIHx8IDB9XHJcbk1vc3QgRGlzY3Vzc2VkIFN1YmplY3Q6ICR7YW5hbHl0aWNzLmNoYXRTdGF0cz8ubW9zdERpc2N1c3NlZFN1YmplY3QgfHwgJ05vIGNoYXQgYWN0aXZpdHknfVxyXG5Ub3RhbCBUaW1lIFNwZW50OiAke2FuYWx5dGljcy5jaGF0U3RhdHM/LnRvdGFsVGltZVNwZW50IHx8ICcwaHIgMG1pbid9XHJcbk1vc3QgQWN0aXZlIFRpbWU6ICR7YW5hbHl0aWNzLmNoYXRTdGF0cz8udGltZU9mRGF5TW9zdEFjdGl2ZSB8fCAnTm8gYWN0aXZpdHknfVxyXG5MZWFybmluZyBTdHJlYWs6ICR7YW5hbHl0aWNzLmNoYXRTdGF0cz8uc3RyZWFrIHx8IDB9IGRheXNcclxuXHJcbkxFQURFUkJPQVJEIFNUQVRTXHJcbi0tLS0tLS0tLS0tLS0tLS1cclxuQ3VycmVudCBSYW5rOiAke2FuYWx5dGljcy5sZWFkZXJib2FyZFN0YXRzPy5jdXJyZW50UmFuayA/IGAjJHthbmFseXRpY3MubGVhZGVyYm9hcmRTdGF0cy5jdXJyZW50UmFua31gIDogJ05vdCByYW5rZWQnfVxyXG5TcGFyayBQb2ludHM6ICR7YW5hbHl0aWNzLmxlYWRlcmJvYXJkU3RhdHM/LnNwYXJrUG9pbnRzIHx8IDB9XHJcblJhbmsgTW92ZW1lbnQ6ICR7YW5hbHl0aWNzLmxlYWRlcmJvYXJkU3RhdHM/LnJhbmtNb3ZlbWVudCB8fCAnTm8gbW92ZW1lbnQnfVxyXG5Nb3RpdmF0aW9uIExldmVsOiAke2FuYWx5dGljcy5sZWFkZXJib2FyZFN0YXRzPy5tb3RpdmF0aW9uTGV2ZWwgfHwgJ0dldHRpbmcgc3RhcnRlZCd9XHJcblxyXG5SRUNFTlQgQUNUSVZJVFlcclxuLS0tLS0tLS0tLS0tLS1cclxuJHthbmFseXRpY3MuYWN0aXZpdHlQYXR0ZXJuPy5kYWlseUFjdGl2aXR5ICYmIGFuYWx5dGljcy5hY3Rpdml0eVBhdHRlcm4uZGFpbHlBY3Rpdml0eS5sZW5ndGggPiAwID9cclxuICBhbmFseXRpY3MuYWN0aXZpdHlQYXR0ZXJuLmRhaWx5QWN0aXZpdHkuc2xpY2UoLTcpLm1hcChhY3Rpdml0eSA9PlxyXG4gICAgYCR7bmV3IERhdGUoYWN0aXZpdHkuZGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9OiAke2FjdGl2aXR5LnF1ZXJpZXN9IHF1ZXJpZXMsICR7YWN0aXZpdHkudGltZVNwZW50fSBtaW4sIFN1YmplY3RzOiAke2FjdGl2aXR5LnN1YmplY3RzLm1hcChzdWJqZWN0SWQgPT4gZ2V0U3ViamVjdERpc3BsYXlOYW1lKHN1YmplY3RJZCkpLmpvaW4oJywgJykgfHwgJ05vbmUnfWBcclxuICApLmpvaW4oJ1xcbicpIDogJ05vIHJlY2VudCBhY3Rpdml0eSd9XHJcbmAgOiAnQW5hbHl0aWNzIGRhdGEgbm90IGF2YWlsYWJsZSd9XHJcblxyXG5SZXBvcnQgZ2VuZXJhdGVkIGJ5IFN0dWR5QnVkZHkgQWRtaW4gRGFzaGJvYXJkXHJcbiAgICBgLnRyaW0oKVxyXG4gIH1cclxuXHJcbiAgaWYgKCFpc09wZW4gfHwgIXN0dWRlbnQpIHJldHVybiBudWxsXHJcblxyXG4gIC8vIEVuaGFuY2VkIHN0dWRlbnQgaW5mbyB3aXRoIGJldHRlciBmYWxsYmFjayBkYXRhXHJcbiAgY29uc3QgZGlzcGxheVN0dWRlbnRJbmZvID0gc3R1ZGVudEluZm8gfHwge1xyXG4gICAgdXNlcklkOiBzdHVkZW50Ll9pZCxcclxuICAgIG5hbWU6IHN0dWRlbnQubmFtZSB8fCBgU3R1ZGVudCAke3N0dWRlbnQuZW1haWwuc3BsaXQoJ0AnKVswXX1gLFxyXG4gICAgcGhvbmU6IHN0dWRlbnQucGhvbmUgfHwgXCJOb3QgcHJvdmlkZWRcIixcclxuICAgIGNsYXNzOiBzdHVkZW50LmNsYXNzIHx8IFwiTm90IHNwZWNpZmllZFwiLFxyXG4gICAgc2Nob29sTmFtZTogc3R1ZGVudC5zY2hvb2xOYW1lIHx8IFwiTm90IHNwZWNpZmllZFwiLFxyXG4gICAgZW1haWw6IHN0dWRlbnQuZW1haWwsXHJcbiAgICBjcmVhdGVkQXQ6IHN0dWRlbnQuY3JlYXRlZEF0IHx8IHN0dWRlbnQuY3JlYXRlZE9uIHx8IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgIHN1YmplY3RzOiBzdHVkZW50LnN1YmplY3RzIHx8IFtdLFxyXG4gICAgcHJvZmlsZUltYWdlOiBzdHVkZW50LmF2YXRhciB8fCBzdHVkZW50LnByb2ZpbGVJbWFnZSB8fCB1bmRlZmluZWRcclxuICB9XHJcblxyXG5cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy14bCB3LWZ1bGwgbWF4LXctNnhsIG14LTQgbWF4LWgtWzk1dmhdIG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cclxuICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXByaW1hcnktYmx1ZVwiIC8+XHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlN0dWRlbnQgRGV0YWlsczwvaDI+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXtmZXRjaFN0dWRlbnRBbmFseXRpY3N9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cclxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YHctNCBoLTQgbXItMiAke2lzTG9hZGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cclxuICAgICAgICAgICAgICBSZWZyZXNoXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRG93bmxvYWR9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRG93bmxvYWRpbmd9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS1ibHVlIGhvdmVyOmJnLXByaW1hcnktYmx1ZS85MCB0ZXh0LXdoaXRlIHB4LTQgcHktMiB0ZXh0LXNtIGZsZXggaXRlbXMtY2VudGVyXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtpc0Rvd25sb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yIGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAge2lzRG93bmxvYWRpbmcgPyAnR2VuZXJhdGluZy4uLicgOiAnRG93bmxvYWQgRGV0YWlscyd9XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxyXG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogQ29udGVudCAqL31cclxuICAgICAgICA8ZGl2IHJlZj17bW9kYWxDb250ZW50UmVmfSBjbGFzc05hbWU9XCJwLTYgc3BhY2UteS04XCI+XHJcbiAgICAgICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwidy04IGgtOCBhbmltYXRlLXNwaW4gdGV4dC1wcmltYXJ5LWJsdWVcIiAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgc3R1ZGVudCBhbmFseXRpY3MuLi48L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgey8qIERhdGEgU291cmNlIEluZGljYXRvciAqL31cclxuICAgICAgICAgICAge2FuYWx5dGljcyAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtMyBiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtzdHVkZW50SW5mbyA/ICdMaXZlIGRhdGEgZnJvbSBhbmFseXRpY3Mgc2VydmljZScgOiAnU2FtcGxlIGRhdGEgLSBBbmFseXRpY3Mgc2VydmljZSB1bmF2YWlsYWJsZSd9XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgey8qIFN0dWRlbnQgUHJvZmlsZSBTZWN0aW9uICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wcmltYXJ5LWRhcmsgcm91bmRlZC1sZyBwLTYgdGV4dC13aGl0ZVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMiBoLTMyIGJnLXByaW1hcnktYmx1ZSByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAge2Rpc3BsYXlTdHVkZW50SW5mby5wcm9maWxlSW1hZ2UgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICAgICAgICBzcmM9e2Rpc3BsYXlTdHVkZW50SW5mby5wcm9maWxlSW1hZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgYWx0PXtkaXNwbGF5U3R1ZGVudEluZm8ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciByb3VuZGVkLWxnXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cInctMTYgaC0xNiB0ZXh0LXdoaXRlXCIgLz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZ3JpZCBncmlkLWNvbHMtMiBnYXAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj5OYW1lPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZGlzcGxheVN0dWRlbnRJbmZvLm5hbWV9PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj5QaG9uZTwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e2Rpc3BsYXlTdHVkZW50SW5mby5waG9uZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPkNsYXNzPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZGlzcGxheVN0dWRlbnRJbmZvLmNsYXNzfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtXCI+U2Nob29sIE5hbWU8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPntkaXNwbGF5U3R1ZGVudEluZm8uc2Nob29sTmFtZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPkVtYWlsPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZGlzcGxheVN0dWRlbnRJbmZvLmVtYWlsfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj5DcmVhdGVkIE9uPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPntuZXcgRGF0ZShkaXNwbGF5U3R1ZGVudEluZm8uY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPlN1YmplY3RzPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2Rpc3BsYXlTdHVkZW50SW5mby5zdWJqZWN0cyAmJiBkaXNwbGF5U3R1ZGVudEluZm8uc3ViamVjdHMubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGRpc3BsYXlTdHVkZW50SW5mby5zdWJqZWN0cy5qb2luKCcsICcpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ05vdCBzcGVjaWZpZWQnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBBbmFseXRpY3MgU2VjdGlvbiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPlN0dWRlbnQgQW5hbHl0aWNzPC9oMz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgey8qIFF1aXogU3RhdHMgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5RdWl6IFBlcmZvcm1hbmNlPC9oND5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlF1aXp6ZXMgQXR0ZW1wdGVkOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2FuYWx5dGljcz8ucXVpelN0YXRzPy50b3RhbEF0dGVtcHRlZCB8fCAwfVxyXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlF1aXogQWNjdXJhY3k6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj57YW5hbHl0aWNzPy5xdWl6U3RhdHM/LmFjY3VyYWN5IHx8IDB9JTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VG9waWNzIENvbXBsZXRlZDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LnF1aXpTdGF0cz8udG9waWNzQ29tcGxldGVkIHx8IDB9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5MYXN0IFF1aXogRGF0ZTo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LnF1aXpTdGF0cz8ubGFzdFF1aXpEYXRlIHx8ICdOL0EnfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAge2FuYWx5dGljcz8ucXVpelN0YXRzPy5hdmVyYWdlU2NvcmVzICYmXHJcbiAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMoYW5hbHl0aWNzLnF1aXpTdGF0cy5hdmVyYWdlU2NvcmVzKS5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAgYW5hbHl0aWNzLnF1aXpTdGF0cy50b3RhbEF0dGVtcHRlZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5TdWJqZWN0LXdpc2UgUGVyZm9ybWFuY2U6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSBzcGFjZS15LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhhbmFseXRpY3MucXVpelN0YXRzLmF2ZXJhZ2VTY29yZXMpLm1hcCgoW3N1YmplY3RLZXksIHNjb3JlXSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17c3ViamVjdEtleX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGNhcGl0YWxpemVcIj57Z2V0U3ViamVjdERpc3BsYXlOYW1lKHN1YmplY3RLZXkpfTo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMFwiPntNYXRoLnJvdW5kKChzY29yZSBhcyBudW1iZXIpICogMTAwKX0lPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBDaGF0IFN0YXRzICovfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+Q2hhdCBBY3Rpdml0eTwvaDQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Ub3RhbCBNZXNzYWdlczo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LmNoYXRTdGF0cz8udG90YWxNZXNzYWdlcyB8fCAwfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VG90YWwgRG91YnRzIEFza2VkOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2FuYWx5dGljcz8uY2hhdFN0YXRzPy50b3RhbERvdWJ0cyB8fCAwfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+TW9zdCBEaXNjdXNzZWQgU3ViamVjdDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHthbmFseXRpY3M/LmNoYXRTdGF0cz8ubW9zdERpc2N1c3NlZFN1YmplY3QgP1xyXG4gICAgICAgICAgICAgICAgICAgICAgZ2V0U3ViamVjdERpc3BsYXlOYW1lKGFuYWx5dGljcy5jaGF0U3RhdHMubW9zdERpc2N1c3NlZFN1YmplY3QpIDogJ04vQSd9XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VG90YWwgVGltZSBTcGVudDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LmNoYXRTdGF0cz8udG90YWxUaW1lU3BlbnQgfHwgJ04vQSd9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5Nb3N0IEFjdGl2ZSBUaW1lOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2FuYWx5dGljcz8uY2hhdFN0YXRzPy50aW1lT2ZEYXlNb3N0QWN0aXZlIHx8ICdOL0EnfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+TGVhcm5pbmcgU3RyZWFrOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2FuYWx5dGljcz8uY2hhdFN0YXRzPy5zdHJlYWsgfHwgMH0gZGF5czwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogTGVhZGVyYm9hcmQgU3RhdHMgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5MZWFkZXJib2FyZCBQb3NpdGlvbjwvaDQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5DdXJyZW50IFJhbms6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7YW5hbHl0aWNzPy5sZWFkZXJib2FyZFN0YXRzPy5jdXJyZW50UmFuayA/IGAjJHthbmFseXRpY3MubGVhZGVyYm9hcmRTdGF0cy5jdXJyZW50UmFua31gIDogJ05vdCByYW5rZWQnfVxyXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlNwYXJrIFBvaW50czo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LmxlYWRlcmJvYXJkU3RhdHM/LnNwYXJrUG9pbnRzIHx8IDB9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5SYW5rIE1vdmVtZW50Ojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2FuYWx5dGljcz8ubGVhZGVyYm9hcmRTdGF0cz8ucmFua01vdmVtZW50IHx8ICdOL0EnfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+TW90aXZhdGlvbiBMZXZlbDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPnthbmFseXRpY3M/LmxlYWRlcmJvYXJkU3RhdHM/Lm1vdGl2YXRpb25MZXZlbCB8fCAnTi9BJ308L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogQ2hhcnRzIFNlY3Rpb24gLSBTaG93IHdoZW4gZGF0YSBleGlzdHMgKi99XHJcbiAgICAgICAgICB7YW5hbHl0aWNzICYmIChcclxuICAgICAgICAgICAgKGFuYWx5dGljcy5xdWl6U3RhdHM/LnN1YmplY3RXaXNlQXR0ZW1wdHMgJiYgT2JqZWN0LmtleXMoYW5hbHl0aWNzLnF1aXpTdGF0cy5zdWJqZWN0V2lzZUF0dGVtcHRzKS5sZW5ndGggPiAwKSB8fFxyXG4gICAgICAgICAgICAoYW5hbHl0aWNzLnF1aXpTdGF0cz8uYXZlcmFnZVNjb3JlcyAmJiBPYmplY3Qua2V5cyhhbmFseXRpY3MucXVpelN0YXRzLmF2ZXJhZ2VTY29yZXMpLmxlbmd0aCA+IDApXHJcbiAgICAgICAgICApICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTZcIj5QZXJmb3JtYW5jZSBDaGFydHM8L2gzPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxyXG4gICAgICAgICAgICAgICAgey8qIFF1aXogRGlzdHJpYnV0aW9uIFBpZSBDaGFydCAqL31cclxuICAgICAgICAgICAgICAgIHthbmFseXRpY3MucXVpelN0YXRzPy5zdWJqZWN0V2lzZUF0dGVtcHRzICYmXHJcbiAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMoYW5hbHl0aWNzLnF1aXpTdGF0cy5zdWJqZWN0V2lzZUF0dGVtcHRzKS5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItNFwiPlF1aXogQXR0ZW1wdHMgYnkgU3ViamVjdDwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFF1aXpEaXN0cmlidXRpb25DaGFydCBkYXRhPXthbmFseXRpY3MucXVpelN0YXRzLnN1YmplY3RXaXNlQXR0ZW1wdHN9IC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogQWNjdXJhY3kgQmFyIENoYXJ0ICovfVxyXG4gICAgICAgICAgICAgICAge2FuYWx5dGljcy5xdWl6U3RhdHM/LmF2ZXJhZ2VTY29yZXMgJiZcclxuICAgICAgICAgICAgICAgICBPYmplY3Qua2V5cyhhbmFseXRpY3MucXVpelN0YXRzLmF2ZXJhZ2VTY29yZXMpLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItNFwiPlN1YmplY3Qtd2lzZSBBY2N1cmFjeTwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPEFjY3VyYWN5QmFyQ2hhcnQgZGF0YT17YW5hbHl0aWNzLnF1aXpTdGF0cy5hdmVyYWdlU2NvcmVzfSAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7LyogQWN0aXZpdHkgU3VtbWFyeSAqL31cclxuICAgICAgICAgIHthbmFseXRpY3M/LmFjdGl2aXR5UGF0dGVybj8uZGFpbHlBY3Rpdml0eSAmJiBhbmFseXRpY3MuYWN0aXZpdHlQYXR0ZXJuLmRhaWx5QWN0aXZpdHkubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTZcIj5SZWNlbnQgQWN0aXZpdHk8L2gzPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAge2FuYWx5dGljcy5hY3Rpdml0eVBhdHRlcm4uZGFpbHlBY3Rpdml0eS5zbGljZSgtNykubWFwKChhY3Rpdml0eSwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKGFjdGl2aXR5LmRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5RdWVyaWVzOiB7YWN0aXZpdHkucXVlcmllc308L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+VGltZToge2FjdGl2aXR5LnRpbWVTcGVudH0gbWluPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlN1YmplY3RzOiB7YWN0aXZpdHkuc3ViamVjdHMubWFwKHN1YmplY3RJZCA9PiBnZXRTdWJqZWN0RGlzcGxheU5hbWUoc3ViamVjdElkKSkuam9pbignLCAnKSB8fCAnTm9uZSd9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6ICFpc0xvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cclxuICAgICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBBY3Rpdml0eSBEYXRhIEF2YWlsYWJsZTwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgQWN0aXZpdHkgZGF0YSB3aWxsIGFwcGVhciBvbmNlIHRoZSBzdHVkZW50IHN0YXJ0cyB1c2luZyB0aGUgcGxhdGZvcm0uXHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoU3R1ZGVudEFuYWx5dGljc31cclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBteC1hdXRvXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICBSZWZyZXNoIERhdGFcclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICA8Lz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJYIiwiRG93bmxvYWQiLCJVc2VyIiwiTWVzc2FnZUNpcmNsZSIsIkxvYWRlcjIiLCJSZWZyZXNoQ3ciLCJCdXR0b24iLCJ1c2VUb2FzdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwianNQREYiLCJodG1sMmNhbnZhcyIsIlN0dWRlbnREZXRhaWxzTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwic3R1ZGVudCIsImFuYWx5dGljcyIsImNvbnNvbGUiLCJsb2ciLCJfaWQiLCJ0b2FzdCIsInNldEFuYWx5dGljcyIsInN0dWRlbnRJbmZvIiwic2V0U3R1ZGVudEluZm8iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc0Rvd25sb2FkaW5nIiwic2V0SXNEb3dubG9hZGluZyIsInN1YmplY3RNYXAiLCJzZXRTdWJqZWN0TWFwIiwic3ViamVjdHNMb2FkaW5nIiwic2V0U3ViamVjdHNMb2FkaW5nIiwibW9kYWxDb250ZW50UmVmIiwiZmV0Y2hTdWJqZWN0cyIsInJlc3BvbnNlIiwiZmV0Y2giLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImhlYWRlcnMiLCJnZXRBdXRoSGVhZGVycyIsIm9rIiwic3ViamVjdHMiLCJqc29uIiwibWFwcGluZyIsImZvckVhY2giLCJzdWJqZWN0IiwiZXJyb3IiLCJmZXRjaFN0dWRlbnRBbmFseXRpY3MiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJnZXRTdWJqZWN0RGlzcGxheU5hbWUiLCJzdWJqZWN0SWQiLCJuYW1lIiwiUXVpekRpc3RyaWJ1dGlvbkNoYXJ0IiwiZGF0YSIsInRvdGFsIiwiT2JqZWN0IiwidmFsdWVzIiwicmVkdWNlIiwic3VtIiwidmFsIiwiY29sb3JzIiwiY3VycmVudEFuZ2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1jaGFydCIsInN0eWxlIiwid2lkdGgiLCJoZWlnaHQiLCJzdmciLCJ2aWV3Qm94IiwiZGlzcGxheSIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwiZW50cmllcyIsIm1hcCIsImluZGV4IiwiY291bnQiLCJwZXJjZW50YWdlIiwiYW5nbGUiLCJzdGFydEFuZ2xlIiwieDEiLCJNYXRoIiwiY29zIiwiUEkiLCJ5MSIsInNpbiIsIngyIiwieTIiLCJsYXJnZUFyY0ZsYWciLCJwYXRoIiwiZCIsImxlbmd0aCIsImJhY2tncm91bmRDb2xvciIsInNwYW4iLCJBY2N1cmFjeUJhckNoYXJ0Iiwia2V5cyIsIm1heFNjb3JlIiwibWF4IiwibWluSGVpZ2h0IiwiaDQiLCJzY29yZSIsInJvdW5kIiwiYmFyV2lkdGgiLCJ1c2VyUmVzcG9uc2UiLCJ1c2VyRGV0YWlscyIsInVzZXJzIiwiZmluZCIsInUiLCJzdGF0dXMiLCJKU09OIiwic3RyaW5naWZ5IiwiY3JlYXRlRmFsbGJhY2tBbmFseXRpY3MiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicXVpelN0YXRzIiwidG90YWxBdHRlbXB0ZWQiLCJhY2N1cmFjeSIsInN1YmplY3RXaXNlQXR0ZW1wdHMiLCJhdmVyYWdlU2NvcmVzIiwibGFzdFF1aXpEYXRlIiwidG9waWNzQ29tcGxldGVkIiwiY2hhdFN0YXRzIiwidG90YWxNZXNzYWdlcyIsInRvdGFsRG91YnRzIiwibW9zdERpc2N1c3NlZFN1YmplY3QiLCJ0b3RhbFRpbWVTcGVudCIsInRpbWVPZkRheU1vc3RBY3RpdmUiLCJzdHJlYWsiLCJsZWFkZXJib2FyZFN0YXRzIiwiY3VycmVudFJhbmsiLCJzcGFya1BvaW50cyIsInJhbmtNb3ZlbWVudCIsIm1vdGl2YXRpb25MZXZlbCIsImFjdGl2aXR5UGF0dGVybiIsImRhaWx5QWN0aXZpdHkiLCJ3ZWVrbHlQYXR0ZXJuIiwibW9udGhseVRyZW5kIiwiZ2VuZXJhdGVQREZSZXBvcnQiLCJjdXJyZW50IiwiZGlzcGxheVN0dWRlbnRJbmZvIiwicGRmIiwicGFnZVdpZHRoIiwiaW50ZXJuYWwiLCJwYWdlU2l6ZSIsImdldFdpZHRoIiwieVBvc2l0aW9uIiwic2V0Rm9udFNpemUiLCJzZXRGb250IiwidGV4dCIsImFsaWduIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImVtYWlsIiwicGhvbmUiLCJjbGFzcyIsInNjaG9vbE5hbWUiLCJjcmVhdGVkQXQiLCJqb2luIiwiaW5mbyIsInN0YXQiLCJzdWJqZWN0S2V5IiwiYWRkUGFnZSIsImNoYXJ0WVBvc2l0aW9uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwicGllQ2hhcnRFbGVtZW50IiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwiYmFyQ2hhcnRFbGVtZW50IiwiY2FudmFzIiwic2NhbGUiLCJ1c2VDT1JTIiwiYWxsb3dUYWludCIsIm9mZnNldFdpZHRoIiwib2Zmc2V0SGVpZ2h0IiwibG9nZ2luZyIsImltZ0RhdGEiLCJ0b0RhdGFVUkwiLCJjaGFydFdpZHRoIiwiY2hhcnRIZWlnaHQiLCJjaGFydFgiLCJhZGRJbWFnZSIsImNoYXJ0RXJyb3IiLCJmaWxlTmFtZSIsInNwbGl0IiwidG9JU09TdHJpbmciLCJzYXZlIiwiaGFuZGxlRG93bmxvYWQiLCJnZW5lcmF0ZVRleHRSZXBvcnQiLCJyZXBvcnREYXRlIiwic3R1ZGVudE5hbWUiLCJzbGljZSIsImFjdGl2aXR5IiwiZGF0ZSIsInF1ZXJpZXMiLCJ0aW1lU3BlbnQiLCJ0cmltIiwidXNlcklkIiwiY3JlYXRlZE9uIiwicHJvZmlsZUltYWdlIiwiYXZhdGFyIiwidW5kZWZpbmVkIiwiaDIiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJ2YXJpYW50Iiwic2l6ZSIsImJ1dHRvbiIsInJlZiIsImltZyIsInNyYyIsImFsdCIsIm9uRXJyb3IiLCJlIiwiY3VycmVudFRhcmdldCIsInAiLCJoMyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/student-details-modal.tsx\n"));

/***/ })

});