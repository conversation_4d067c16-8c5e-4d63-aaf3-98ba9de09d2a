"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseDatePipe = void 0;
class ParseDatePipe {
    transform(value) {
        if (value) {
            try {
                const parsedDate = new Date(value);
                if (isNaN(parsedDate.getTime())) {
                    return value;
                }
                return parsedDate.toISOString().split('T')[0];
            }
            catch (error) {
                return value;
            }
        }
        return value;
    }
}
exports.ParseDatePipe = ParseDatePipe;
//# sourceMappingURL=chatHistoryQueryDto.js.map