{"version": 3, "file": "leaderboard.service.js", "sourceRoot": "", "sources": ["../../src/leaderboard/leaderboard.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,+CAA+C;AAC/C,uCAAiC;AACjC,wDAA+C;AAC/C,sEAA6D;AAC7D,sEAA6D;AA2BtD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,YACkC,SAAsB,EACf,gBAAoC,EACpC,gBAAoC;QAF3C,cAAS,GAAT,SAAS,CAAa;QACf,qBAAgB,GAAhB,gBAAgB,CAAoB;QACpC,qBAAgB,GAAhB,gBAAgB,CAAoB;IAC1E,CAAC;IAEI,YAAY,CAAC,MAAoC;QACvD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAErC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACrC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC;YAE5D,KAAK,SAAS;gBACZ,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;gBACjC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACxC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC;YAE7D,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,IAAU;QAC3B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,oBAAoB,CAAC,WAAmB,EAAE,YAAoB,EAAE,MAAc;QAKpF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;QACjD,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC;QACpC,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,gBAAgB,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,aAAqB,EACrB,SAAuC,KAAK,EAC5C,OAAgB,EAChB,WAAoB,EACpB,QAAgB,EAAE;QAElB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAGzD,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;QACvD,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,UAAU,CAAC,QAAQ,GAAG,OAAO,CAAC;QAChC,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YACtD,EAAE,MAAM,EAAE,UAAU,EAAE;YACtB;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE,SAAS;oBACd,WAAW,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC1C,YAAY,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;oBACpE,cAAc,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE;oBAC1C,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;iBACpB;aACF;YACD;gBACE,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;oBACd,WAAW,EAAE,CAAC;oBACd,YAAY,EAAE,CAAC;oBACf,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE;oBAC/G,MAAM,EAAE,CAAC;oBACT,WAAW,EAAE;wBACX,MAAM,EAAE;4BACN,SAAS,EAAE;gCACT,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gCACnF,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;6BAC9D;yBACF;qBACF;iBACF;aACF;YACD,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE;SAC/B,CAAC,CAAC;QAGH,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC;aAC/D,QAAQ,CAAC,aAAa,CAAC;aACvB,IAAI,EAAE,CAAC;QAGV,IAAI,gBAAgB,GAAsB,EAAE,CAAC;QAE7C,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9E,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,SAAS;YAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAkB,CAAC;YAG5C,IAAI,WAAW,IAAI,WAAW,CAAC,KAAK,KAAK,WAAW;gBAAE,SAAS;YAE/D,gBAAgB,CAAC,IAAI,CAAC;gBACpB,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC3B,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,SAAS,EAAE,WAAW,CAAC,OAAO;gBAC9B,YAAY,EAAE,WAAW,CAAC,YAAY;gBACtC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,MAAM,EAAE,QAAQ,CAAC,MAAM;aACxB,CAAC,CAAC;QACL,CAAC;QAGD,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;QAC/D,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACvC,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAGtD,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;QAEjF,OAAO;YACL,KAAK,EAAE,YAAY;YACnB,WAAW;YACX,UAAU,EAAE,gBAAgB,CAAC,MAAM;YACnC,MAAM;YACN,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CACf,aAAqB,EACrB,SAAuC,KAAK,EAC5C,OAAgB,EAChB,WAAoB;QAEpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACjG,OAAO,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,WAAW,CACf,aAAqB,EACrB,WAAmB,EACnB,SAAuC,KAAK,EAC5C,OAAgB,EAChB,WAAoB;QAEpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAEjG,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACpD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC3D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CACrC,CAAC;QAEF,OAAO;YACL,GAAG,WAAW;YACd,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YACjC,UAAU,EAAE,aAAa,CAAC,MAAM;SACjC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,aAAqB,EACrB,SAAuC,KAAK,EAC5C,OAAgB,EAChB,WAAoB;QAEpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QAE9F,OAAO;YACL,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACvC,WAAW,EAAE,WAAW,CAAC,WAAW;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA/LY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;IACtB,WAAA,IAAA,sBAAW,EAAC,gCAAW,CAAC,IAAI,CAAC,CAAA;IAC7B,WAAA,IAAA,sBAAW,EAAC,gCAAW,CAAC,IAAI,CAAC,CAAA;qCAFa,gBAAK;QACS,gBAAK;QACL,gBAAK;GAJrD,kBAAkB,CA+L9B"}